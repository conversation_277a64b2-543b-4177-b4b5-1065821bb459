# LaundrySense - Quick Start Guide

## 🚀 Phase 1 Setup (Foundation & Core Engine)

### 1. Prerequisites Check
```bash
# Check Node.js version (should be 18+)
node --version

# Check MySQL (should be 8.0+)
mysql --version

# Check npm
npm --version
```

### 2. Database Setup
```bash
# Create MySQL database
mysql -u root -e "CREATE DATABASE laundrysense;"

# Optional: Create test database
mysql -u root -e "CREATE DATABASE laundrysense_test;"
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your database credentials (without password)
# DATABASE_URL="mysql://root@localhost:3306/laundrysense"
```

### 4. Install Dependencies
```bash
# Install all project dependencies
npm install

# This will install:
# - Next.js 15 with React 19
# - Prisma ORM with PostgreSQL client
# - Zod for validation
# - Jest for testing
# - TypeScript and Tailwind CSS
```

### 5. Database Migration & Seeding
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (development)
npm run db:push

# Seed database with sample data
npm run db:seed

# Expected output:
# 🧴 Seeding materials...
# ✅ Created 10 materials
# 👥 Seeding customers...
# ✅ Created 30 customers
# 💰 Seeding transactions...
# ✅ Created 150 transactions with materials
# 🎉 Database seeding completed successfully!
```

### 6. Start Development Server
```bash
# Start Next.js development server
npm run dev

# Server will be available at:
# http://localhost:3000
```

### 7. Verify Installation
```bash
# Test API endpoints
curl http://localhost:3000/api/customers
curl http://localhost:3000/api/materials
curl http://localhost:3000/api/transactions

# Run tests
npm test

# Expected: All tests should pass
```

## 📊 Sample Data Overview

After seeding, your database will contain:

### Materials (10 items)
- Deterjen Cair Premium (50L)
- Pewangi Pakaian Lavender (30L)
- Pelembut Kain (25L)
- Pemutih Pakaian (15L)
- Penghilang Noda (20L)
- Plastik Pembungkus (500 sheets)
- Hanger Plastik (200 pieces)
- Deterjen Bubuk Ekonomis (40kg)
- Pewangi Rose (20L)
- Oli Mesin Cuci (10L)

### Customers (30 profiles)
- Diverse customer base with realistic names
- Phone numbers and email addresses
- Behavioral intelligence scores
- Registration dates spanning 2023-2024

### Transactions (150 records)
- 6 months of transaction history
- Various service types (wash, iron, dry clean, etc.)
- Contextual data (weather, day type, time)
- Material usage tracking
- Realistic pricing and weights

## 🔧 Development Commands

```bash
# Database Management
npm run db:generate    # Generate Prisma client
npm run db:push       # Push schema changes (dev)
npm run db:migrate    # Run migrations (prod)
npm run db:seed       # Seed sample data
npm run db:reset      # Reset database

# Development
npm run dev           # Start dev server
npm run build         # Build for production
npm run start         # Start production server
npm run lint          # Run ESLint

# Testing
npm test              # Run all tests
npm run test:watch    # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

## 🧪 API Testing Examples

### Create a Customer
```bash
curl -X POST http://localhost:3000/api/customers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Customer",
    "phone_number": "081234567890",
    "email": "<EMAIL>",
    "address": "Test Address 123"
  }'
```

### Get Customers (Paginated)
```bash
curl "http://localhost:3000/api/customers?page=1&limit=5&search=Budi"
```

### Create a Transaction
```bash
curl -X POST http://localhost:3000/api/transactions \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": "CUSTOMER_ID_FROM_ABOVE",
    "service_type": "CUCI_SETRIKA",
    "weight_kg": 3.5,
    "price": 35000,
    "context_weather": "SUNNY",
    "context_day_type": "WEEKDAY"
  }'
```

### Check Material Stock
```bash
curl "http://localhost:3000/api/materials?category=DETERGENT&lowStock=true"
```

## 🎯 What's Next?

Phase 1 is now complete! You have:
- ✅ Robust database schema with contextual intelligence
- ✅ Complete CRUD APIs for all entities
- ✅ Sample data for testing and development
- ✅ Comprehensive testing framework
- ✅ Documentation and setup guides

### Ready for Phase 2: Contextual Intelligence Layer
- Pattern detection algorithms
- Smart insight generation
- Context-aware recommendations
- Behavioral analysis engine

## 🆘 Troubleshooting

### Database Connection Issues
```bash
# Check MySQL is running
sudo service mysql status

# Check database exists
mysql -u root -e "SHOW DATABASES;" | grep laundrysense

# Test connection
mysql -u root -e "USE laundrysense; SHOW TABLES;"
```

### Port Already in Use
```bash
# Kill process on port 3000
lsof -ti:3000 | xargs kill -9

# Or use different port
PORT=3001 npm run dev
```

### Prisma Issues
```bash
# Reset Prisma client
rm -rf node_modules/.prisma
npm run db:generate

# Reset database completely
npm run db:reset
```

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review the detailed documentation in `/docs`
3. Check the development progress for current status
4. Verify all prerequisites are met

---

**🎉 Congratulations! LaundrySense Phase 1 is now ready for development and testing.**
