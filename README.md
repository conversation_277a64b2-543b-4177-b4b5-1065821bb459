# LaundrySense 🧺

**Contextual Intelligence Laundry Management Dashboard**

LaundrySense is an innovative laundry management system that goes beyond traditional CRUD operations by implementing "Contextual Intelligence Layer" - a smart system that understands business context and provides relevant, timely insights without overwhelming users with notification fatigue.

## 🎯 Project Vision

Transform laundry business management through intelligent, context-aware insights that adapt to:
- **Time Context**: Morning operations vs. evening reflections
- **Business Cycles**: Peak seasons vs. quiet periods
- **User Behavior**: Proactive vs. reactive management styles
- **Data Quality**: Gradual information disclosure from summary to actionable details

## 🏗️ Architecture Overview

### Phase-Based Development
- **Phase 1**: Foundation & Core Engine ✅
- **Phase 2**: Contextual Intelligence Layer 🔄
- **Phase 3**: Progressive UI Development 🔄
- **Phase 4**: Polish & Production Ready 🔄

### Technology Stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS v4
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: MySQL with contextual intelligence schema
- **Testing**: Jest with comprehensive API coverage
- **Validation**: Zod for type-safe data validation

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MySQL 8.0+
- npm or yarn

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd laundrysense

# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your database credentials

# Setup database
npm run db:generate
npm run db:push
npm run db:seed

# Start development server
npm run dev
```

Visit [http://localhost:3000](http://localhost:3000) to see the application.

## 📊 Current Features (Phase 1)

### Core Data Management
- **Customer Management**: Behavioral intelligence scoring
- **Material Inventory**: Smart stock tracking with usage patterns
- **Transaction Processing**: Contextual data capture
- **Pattern Detection**: Foundation for behavioral analysis

### API Endpoints
- `GET/POST/PUT/DELETE /api/customers` - Customer management
- `GET/POST/PUT/DELETE /api/materials` - Inventory management
- `GET/POST/PUT/DELETE /api/transactions` - Transaction processing
- Advanced filtering, pagination, and search capabilities

### Contextual Intelligence Foundation
- Weather context integration
- Day type classification (weekday/weekend/holiday)
- Time-of-day pattern recognition
- Seasonal factor calculation
- Customer behavioral scoring

## 🧪 Testing

```bash
# Run all tests
npm test

# Watch mode for development
npm run test:watch

# Coverage report
npm run test:coverage
```

## 📚 Documentation

- **[Phase 1 Setup Guide](docs/PHASE_1_SETUP.md)** - Detailed setup instructions
- **[Development Progress](docs/DEVELOPMENT_PROGRESS.md)** - Project progress tracking
- **[API Documentation](docs/API_REFERENCE.md)** - Complete API reference (coming soon)

## 🔮 Upcoming Features

### Phase 2: Contextual Intelligence Layer
- Smart insight generation engine
- Pattern detection algorithms
- Context-aware recommendations
- Behavioral analysis dashboard

### Phase 3: Progressive UI Development
- Context-aware dashboard interface
- Real-time notifications and updates
- Gamification elements
- Progressive Web App features

### Phase 4: Production Optimization
- Performance optimization
- Security hardening
- Deployment automation
- Monitoring and analytics

## 🤝 Contributing

This project follows a structured development approach. Please refer to the development progress documentation for current phase objectives and contribution guidelines.

## 📄 License

[MIT License](LICENSE) - Feel free to use this project for your laundry business needs.

---

**Built with ❤️ for laundry business owners who deserve intelligent, context-aware management tools.**
