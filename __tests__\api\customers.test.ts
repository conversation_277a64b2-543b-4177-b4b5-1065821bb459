import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { PrismaClient } from '@/generated/prisma';

// Mock Next.js request/response
const mockRequest = (method: string, body?: any, searchParams?: URLSearchParams) => ({
  method,
  json: async () => body,
  url: `http://localhost:3000/api/customers${searchParams ? `?${searchParams.toString()}` : ''}`,
});

const mockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

// Import the API handlers
import { GET, POST } from '@/app/api/customers/route';
import { GET as getById, PUT, DELETE } from '@/app/api/customers/[id]/route';

const prisma = new PrismaClient();

describe('/api/customers', () => {
  let testCustomerId: string;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081999'
        }
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081999'
        }
      }
    });
    await prisma.$disconnect();
  });

  describe('POST /api/customers', () => {
    it('should create a new customer with valid data', async () => {
      const customerData = {
        name: 'Test Customer',
        phone_number: '081999123456',
        email: '<EMAIL>',
        address: 'Test Address 123',
        behavior_frequency_score: 0.5,
        behavior_preference_score: 0.7,
        behavior_seasonal_bias: 0.3,
      };

      const request = mockRequest('POST', customerData) as any;
      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result.success).toBe(true);
      expect(result.data.name).toBe(customerData.name);
      expect(result.data.phone_number).toBe(customerData.phone_number);
      expect(result.data.email).toBe(customerData.email);

      testCustomerId = result.data.id;
    });

    it('should reject duplicate phone number', async () => {
      const customerData = {
        name: 'Another Customer',
        phone_number: '081999123456', // Same as above
        email: '<EMAIL>',
      };

      const request = mockRequest('POST', customerData) as any;
      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toContain('phone number already exists');
    });

    it('should reject invalid data', async () => {
      const invalidData = {
        name: '', // Empty name
        phone_number: 'invalid-phone',
        email: 'invalid-email',
      };

      const request = mockRequest('POST', invalidData) as any;
      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Validation failed');
    });
  });

  describe('GET /api/customers', () => {
    it('should return paginated customers', async () => {
      const searchParams = new URLSearchParams({
        page: '1',
        limit: '10',
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });

    it('should search customers by name', async () => {
      const searchParams = new URLSearchParams({
        search: 'Test Customer',
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
      expect(result.data[0].name).toContain('Test Customer');
    });
  });

  describe('GET /api/customers/[id]', () => {
    it('should return customer by ID', async () => {
      const request = mockRequest('GET') as any;
      const response = await getById(request, { params: { id: testCustomerId } });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(testCustomerId);
      expect(result.data.name).toBe('Test Customer');
    });

    it('should return 404 for non-existent customer', async () => {
      const request = mockRequest('GET') as any;
      const response = await getById(request, { params: { id: 'non-existent-id' } });
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Customer not found');
    });
  });

  describe('PUT /api/customers/[id]', () => {
    it('should update customer with valid data', async () => {
      const updateData = {
        name: 'Updated Test Customer',
        address: 'Updated Address 456',
        behavior_frequency_score: 0.8,
      };

      const request = mockRequest('PUT', updateData) as any;
      const response = await PUT(request, { params: { id: testCustomerId } });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.name).toBe(updateData.name);
      expect(result.data.address).toBe(updateData.address);
      expect(result.data.behavior_frequency_score).toBe(updateData.behavior_frequency_score);
    });

    it('should reject invalid update data', async () => {
      const invalidData = {
        phone_number: 'invalid-phone',
        behavior_frequency_score: 1.5, // Out of range
      };

      const request = mockRequest('PUT', invalidData) as any;
      const response = await PUT(request, { params: { id: testCustomerId } });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Validation failed');
    });
  });

  describe('DELETE /api/customers/[id]', () => {
    it('should delete customer without transactions', async () => {
      const request = mockRequest('DELETE') as any;
      const response = await DELETE(request, { params: { id: testCustomerId } });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.message).toBe('Customer deleted successfully');

      // Verify customer is deleted
      const deletedCustomer = await prisma.customer.findUnique({
        where: { id: testCustomerId }
      });
      expect(deletedCustomer).toBeNull();
    });

    it('should return 404 for non-existent customer', async () => {
      const request = mockRequest('DELETE') as any;
      const response = await DELETE(request, { params: { id: 'non-existent-id' } });
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Customer not found');
    });
  });
});
