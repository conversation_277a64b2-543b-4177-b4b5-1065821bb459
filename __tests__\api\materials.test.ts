import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient, MaterialCategory } from '@/generated/prisma';

// Mock Next.js request/response
const mockRequest = (method: string, body?: any, searchParams?: URLSearchParams) => ({
  method,
  json: async () => body,
  url: `http://localhost:3000/api/materials${searchParams ? `?${searchParams.toString()}` : ''}`,
});

// Import the API handlers
import { GET, POST } from '@/app/api/materials/route';
import { GET as getById, PUT, DELETE } from '@/app/api/materials/[id]/route';

const prisma = new PrismaClient();

describe('/api/materials', () => {
  let testMaterialId: string;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.materialInventory.deleteMany({
      where: {
        material_name: {
          startsWith: 'Test Material'
        }
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.materialInventory.deleteMany({
      where: {
        material_name: {
          startsWith: 'Test Material'
        }
      }
    });
    await prisma.$disconnect();
  });

  describe('POST /api/materials', () => {
    it('should create a new material with valid data', async () => {
      const materialData = {
        material_name: 'Test Material Detergent',
        current_stock_unit: 100.0,
        unit_of_measure: 'liter',
        usage_rate_per_transaction: 0.2,
        usage_rate_per_kg: 0.1,
        minimum_stock_threshold: 20.0,
        cost_per_unit: 15000,
        supplier_info: 'Test Supplier',
        category: MaterialCategory.DETERGENT,
      };

      const request = mockRequest('POST', materialData) as any;
      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(201);
      expect(result.success).toBe(true);
      expect(result.data.material_name).toBe(materialData.material_name);
      expect(result.data.current_stock_unit).toBe(materialData.current_stock_unit);
      expect(result.data.category).toBe(materialData.category);

      testMaterialId = result.data.id;
    });

    it('should reject duplicate material name', async () => {
      const materialData = {
        material_name: 'Test Material Detergent', // Same as above
        current_stock_unit: 50.0,
        unit_of_measure: 'liter',
        category: MaterialCategory.DETERGENT,
      };

      const request = mockRequest('POST', materialData) as any;
      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Material with this name already exists');
    });

    it('should reject invalid data', async () => {
      const invalidData = {
        material_name: '', // Empty name
        current_stock_unit: -10, // Negative stock
        unit_of_measure: '',
        category: 'INVALID_CATEGORY',
      };

      const request = mockRequest('POST', invalidData) as any;
      const response = await POST(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Validation failed');
    });
  });

  describe('GET /api/materials', () => {
    it('should return paginated materials', async () => {
      const searchParams = new URLSearchParams({
        page: '1',
        limit: '10',
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });

    it('should filter materials by category', async () => {
      const searchParams = new URLSearchParams({
        category: MaterialCategory.DETERGENT,
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      result.data.forEach((material: any) => {
        expect(material.category).toBe(MaterialCategory.DETERGENT);
      });
    });

    it('should search materials by name', async () => {
      const searchParams = new URLSearchParams({
        search: 'Test Material',
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await GET(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.length).toBeGreaterThan(0);
      expect(result.data[0].material_name).toContain('Test Material');
    });
  });

  describe('GET /api/materials/[id]', () => {
    it('should return material by ID', async () => {
      const request = mockRequest('GET') as any;
      const response = await getById(request, { params: { id: testMaterialId } });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(testMaterialId);
      expect(result.data.material_name).toBe('Test Material Detergent');
      expect(result.data.stock_status).toBeDefined();
      expect(result.data.usage_stats).toBeDefined();
    });

    it('should return 404 for non-existent material', async () => {
      const request = mockRequest('GET') as any;
      const response = await getById(request, { params: { id: 'non-existent-id' } });
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Material not found');
    });
  });

  describe('PUT /api/materials/[id]', () => {
    it('should update material with valid data', async () => {
      const updateData = {
        material_name: 'Updated Test Material Detergent',
        current_stock_unit: 150.0,
        cost_per_unit: 18000,
        minimum_stock_threshold: 25.0,
      };

      const request = mockRequest('PUT', updateData) as any;
      const response = await PUT(request, { params: { id: testMaterialId } });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.material_name).toBe(updateData.material_name);
      expect(result.data.current_stock_unit).toBe(updateData.current_stock_unit);
      expect(result.data.cost_per_unit).toBe(updateData.cost_per_unit);
    });

    it('should reject invalid update data', async () => {
      const invalidData = {
        current_stock_unit: -50, // Negative stock
        cost_per_unit: -1000, // Negative cost
      };

      const request = mockRequest('PUT', invalidData) as any;
      const response = await PUT(request, { params: { id: testMaterialId } });
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Validation failed');
    });
  });

  describe('DELETE /api/materials/[id]', () => {
    it('should delete material without transaction history', async () => {
      const request = mockRequest('DELETE') as any;
      const response = await DELETE(request, { params: { id: testMaterialId } });
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.message).toBe('Material deleted successfully');

      // Verify material is deleted
      const deletedMaterial = await prisma.materialInventory.findUnique({
        where: { id: testMaterialId }
      });
      expect(deletedMaterial).toBeNull();
    });

    it('should return 404 for non-existent material', async () => {
      const request = mockRequest('DELETE') as any;
      const response = await DELETE(request, { params: { id: 'non-existent-id' } });
      const result = await response.json();

      expect(response.status).toBe(404);
      expect(result.success).toBe(false);
      expect(result.error).toBe('Material not found');
    });
  });
});
