import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient, ServiceType, MaterialCategory } from '@/generated/prisma';

// Mock Next.js request/response
const mockRequest = (method: string, body?: any, searchParams?: URLSearchParams) => ({
  method,
  json: async () => body,
  url: `http://localhost:3000/api/patterns${searchParams ? `?${searchParams.toString()}` : ''}`,
});

// Import the API handlers
import { GET as getCustomerPatterns, POST as postCustomerPatterns } from '@/app/api/patterns/customers/route';
import { GET as getMaterialPatterns, POST as postMaterialPatterns } from '@/app/api/patterns/materials/route';
import { GET as getRevenuePatterns, POST as postRevenuePatterns } from '@/app/api/patterns/revenue/route';

const prisma = new PrismaClient();

describe('/api/patterns', () => {
  let testCustomerId: string;
  let testMaterialId: string;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.customerPattern.deleteMany({
      where: {
        customer: {
          phone_number: {
            startsWith: '081777'
          }
        }
      }
    });

    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081777'
        }
      }
    });

    await prisma.materialUsagePattern.deleteMany({
      where: {
        material: {
          material_name: {
            startsWith: 'Test API Pattern'
          }
        }
      }
    });

    await prisma.materialInventory.deleteMany({
      where: {
        material_name: {
          startsWith: 'Test API Pattern'
        }
      }
    });

    // Create test data
    const customer = await prisma.customer.create({
      data: {
        name: 'Test API Pattern Customer',
        phone_number: '081777666555',
        email: '<EMAIL>'
      }
    });
    testCustomerId = customer.id;

    const material = await prisma.materialInventory.create({
      data: {
        material_name: 'Test API Pattern Material',
        current_stock_unit: 100.0,
        unit_of_measure: 'liter',
        category: MaterialCategory.DETERGENT
      }
    });
    testMaterialId = material.id;

    // Create some transactions for pattern analysis
    for (let i = 0; i < 5; i++) {
      const transaction = await prisma.transaction.create({
        data: {
          customer_id: testCustomerId,
          service_type: ServiceType.CUCI_SETRIKA,
          weight_kg: 3.0,
          price: 30000,
          transaction_date: new Date(Date.now() - (i * 7 * 24 * 60 * 60 * 1000)) // Weekly intervals
        }
      });

      await prisma.transactionMaterial.create({
        data: {
          transaction_id: transaction.id,
          material_id: testMaterialId,
          quantity_used: 0.3,
          cost_at_time: 4500
        }
      });
    }
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.transactionMaterial.deleteMany({
      where: {
        material_id: testMaterialId
      }
    });

    await prisma.transaction.deleteMany({
      where: {
        customer_id: testCustomerId
      }
    });

    await prisma.customerPattern.deleteMany({
      where: {
        customer_id: testCustomerId
      }
    });

    await prisma.materialUsagePattern.deleteMany({
      where: {
        material_id: testMaterialId
      }
    });

    await prisma.customer.delete({
      where: { id: testCustomerId }
    });

    await prisma.materialInventory.delete({
      where: { id: testMaterialId }
    });

    await prisma.$disconnect();
  });

  describe('GET /api/patterns/customers', () => {
    it('should return customer patterns with pagination', async () => {
      const searchParams = new URLSearchParams({
        page: '1',
        limit: '10'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getCustomerPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
    });

    it('should filter by confidence score', async () => {
      const searchParams = new URLSearchParams({
        min_confidence: '0.5'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getCustomerPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      
      if (result.data.length > 0) {
        result.data.forEach((pattern: any) => {
          expect(pattern.confidence_score).toBeGreaterThanOrEqual(0.5);
        });
      }
    });
  });

  describe('POST /api/patterns/customers/calculate', () => {
    it('should calculate pattern for specific customer', async () => {
      const requestBody = {
        customer_id: testCustomerId
      };

      const request = mockRequest('POST', requestBody) as any;
      const response = await postCustomerPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.customer_id).toBe(testCustomerId);
    });

    it('should trigger calculation for all customers', async () => {
      const requestBody = {};

      const request = mockRequest('POST', requestBody) as any;
      const response = await postCustomerPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.calculation_type).toBe('customer_patterns');
    });

    it('should handle insufficient data gracefully', async () => {
      // Create customer with no transactions
      const customer = await prisma.customer.create({
        data: {
          name: 'No Transactions Customer',
          phone_number: '081777555444',
          email: '<EMAIL>'
        }
      });

      const requestBody = {
        customer_id: customer.id
      };

      const request = mockRequest('POST', requestBody) as any;
      const response = await postCustomerPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Insufficient data');

      // Clean up
      await prisma.customer.delete({ where: { id: customer.id } });
    });
  });

  describe('GET /api/patterns/materials', () => {
    it('should return material patterns with pagination', async () => {
      const searchParams = new URLSearchParams({
        page: '1',
        limit: '10'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getMaterialPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
    });

    it('should filter by reorder recommendation', async () => {
      const searchParams = new URLSearchParams({
        reorder_only: 'true'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getMaterialPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      
      if (result.data.length > 0) {
        result.data.forEach((pattern: any) => {
          expect(pattern.reorder_recommendation).toBe(true);
        });
      }
    });
  });

  describe('POST /api/patterns/materials/calculate', () => {
    it('should calculate pattern for specific material', async () => {
      const requestBody = {
        material_id: testMaterialId
      };

      const request = mockRequest('POST', requestBody) as any;
      const response = await postMaterialPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.material_id).toBe(testMaterialId);
    });
  });

  describe('GET /api/patterns/revenue', () => {
    it('should return revenue patterns with pagination', async () => {
      const searchParams = new URLSearchParams({
        page: '1',
        limit: '30'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getRevenuePatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
      expect(result.summary).toBeDefined();
    });

    it('should filter by date range', async () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      const endDate = new Date();

      const searchParams = new URLSearchParams({
        date_from: startDate.toISOString().split('T')[0],
        date_to: endDate.toISOString().split('T')[0]
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getRevenuePatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
    });

    it('should filter by trend type', async () => {
      const searchParams = new URLSearchParams({
        trend_type: 'peak'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getRevenuePatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      
      if (result.data.length > 0) {
        result.data.forEach((trend: any) => {
          expect(trend.revenue_trend).toBe('peak');
        });
      }
    });
  });

  describe('POST /api/patterns/revenue/calculate', () => {
    it('should calculate revenue trends for date range', async () => {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 7);
      const endDate = new Date();

      const requestBody = {
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString()
      };

      const request = mockRequest('POST', requestBody) as any;
      const response = await postRevenuePatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.records_processed).toBeGreaterThanOrEqual(0);
    });

    it('should trigger calculation for default range', async () => {
      const requestBody = {};

      const request = mockRequest('POST', requestBody) as any;
      const response = await postRevenuePatterns(request);
      const result = await response.json();

      expect(response.status).toBe(200);
      expect(result.success).toBe(true);
      expect(result.data.calculation_type).toBe('revenue_trends');
    });
  });

  describe('error handling', () => {
    it('should handle invalid request data', async () => {
      const requestBody = {
        customer_id: 'invalid-id',
        invalid_field: 'invalid_value'
      };

      const request = mockRequest('POST', requestBody) as any;
      const response = await postCustomerPatterns(request);
      const result = await response.json();

      expect(response.status).toBe(400);
      expect(result.success).toBe(false);
    });

    it('should handle server errors gracefully', async () => {
      // This test ensures error handling works
      const searchParams = new URLSearchParams({
        page: 'invalid',
        limit: 'invalid'
      });

      const request = mockRequest('GET', undefined, searchParams) as any;
      const response = await getCustomerPatterns(request);
      
      // Should handle invalid pagination gracefully
      expect(response.status).toBe(200);
    });
  });
});
