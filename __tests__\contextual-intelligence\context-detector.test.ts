/**
 * Context Detector Unit Tests
 *
 * Comprehensive testing for the Context Detection System
 */

import { ContextDetector } from '../../src/lib/contextual-intelligence/context-detector';
import {
  ContextDetectionInput,
  UserActivity,
  HistoricalBusinessData,
  DataCompletenessMetrics,
  PatternData
} from '../../src/lib/contextual-intelligence/types';

describe('ContextDetector', () => {
  let contextDetector: ContextDetector;
  let baseInput: ContextDetectionInput;

  beforeEach(() => {
    contextDetector = new ContextDetector();

    // Create base test input
    baseInput = {
      currentTimestamp: new Date('2024-01-15T10:30:00Z'), // Monday 10:30 AM
      recentUserActivityLog: [
        {
          timestamp: '2024-01-15T10:25:00Z',
          action: 'view_dashboard',
          user_id: 'user1'
        },
        {
          timestamp: '2024-01-15T10:20:00Z',
          action: 'view_transactions',
          user_id: 'user1'
        }
      ],
      historicalBusinessData: {
        monthly_transactions: [
          { month: '2024-01', count: 150, revenue: 7500000 },
          { month: '2023-12', count: 120, revenue: 6000000 }
        ],
        material_usage_averages: [
          {
            material_id: 'mat1',
            material_name: 'Detergent',
            average_monthly_usage: 50,
            category: 'DETERGENT'
          }
        ],
        customer_metrics: {
          total_customers: 100,
          active_customers_last_30_days: 80,
          average_transaction_value: 50000,
          customer_retention_rate: 0.85
        },
        revenue_trends: {
          daily_average: 250000,
          weekly_average: 1750000,
          monthly_average: 7500000,
          growth_rate_percentage: 15.5
        }
      },
      dataCompletenessMetrics: {
        transactions: {
          last_updated: '2024-01-15T09:00:00Z',
          completeness_percentage: 95,
          total_records: 1500,
          missing_fields_count: 5
        },
        customers: {
          last_updated: '2024-01-15T08:00:00Z',
          completeness_percentage: 98,
          total_records: 100,
          missing_fields_count: 2
        },
        materials: {
          last_updated: '2024-01-15T07:00:00Z',
          completeness_percentage: 90,
          total_records: 20,
          missing_fields_count: 2
        },
        patterns: {
          last_calculated: '2024-01-15T02:00:00Z',
          customer_patterns_count: 80,
          material_patterns_count: 18,
          revenue_trends_count: 30,
          average_confidence_score: 0.85
        }
      },
      patternData: {
        customer_patterns: {
          total_analyzed: 80,
          high_confidence_count: 65,
          average_loyalty_score: 0.75,
          average_frequency_score: 0.68,
          seasonal_variance_average: 0.3
        },
        material_patterns: {
          total_analyzed: 18,
          reorder_recommendations_count: 3,
          average_efficiency_score: 0.82,
          stock_alerts_count: 2
        },
        revenue_patterns: {
          peak_days_count: 8,
          growth_trend: 'increasing',
          seasonal_factor_average: 0.6,
          revenue_volatility: 0.15
        }
      }
    };
  });

  describe('Time Context Detection', () => {
    test('should detect morning context correctly', () => {
      const morningInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-15T09:30:00Z') // 9:30 AM
      };

      const result = contextDetector.detectContext(morningInput);

      expect(result.currentContextObject.time).toBe('morning');
      expect(result.contextReasons.time).toContain('operational preparation');
    });

    test('should detect midday context correctly', () => {
      const middayInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-15T14:00:00Z') // 2:00 PM
      };

      const result = contextDetector.detectContext(middayInput);

      expect(result.currentContextObject.time).toBe('midday');
      expect(result.contextReasons.time).toContain('Peak business hours');
    });

    test('should detect evening context correctly', () => {
      const eveningInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-15T19:00:00Z') // 7:00 PM
      };

      const result = contextDetector.detectContext(eveningInput);

      expect(result.currentContextObject.time).toBe('evening');
      expect(result.contextReasons.time).toContain('completion and review');
    });

    test('should detect planning context for weekend', () => {
      const weekendInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-14T10:00:00Z') // Sunday 10:00 AM
      };

      const result = contextDetector.detectContext(weekendInput);

      expect(result.currentContextObject.time).toBe('planning');
      expect(result.contextReasons.time).toContain('strategic planning');
    });

    test('should detect planning context for late night', () => {
      const lateNightInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-15T23:00:00Z') // 11:00 PM
      };

      const result = contextDetector.detectContext(lateNightInput);

      expect(result.currentContextObject.time).toBe('planning');
    });
  });

  describe('Business Cycle Context Detection', () => {
    test('should detect peak season correctly', () => {
      const peakSeasonInput = {
        ...baseInput,
        patternData: {
          ...baseInput.patternData,
          revenue_patterns: {
            ...baseInput.patternData.revenue_patterns,
            seasonal_factor_average: 0.8 // Above peak threshold (0.7)
          }
        }
      };

      const result = contextDetector.detectContext(peakSeasonInput);

      expect(result.currentContextObject.businessCycle).toBe('peak_season');
      expect(result.contextReasons.businessCycle).toContain('High seasonal activity');
    });

    test('should detect low season correctly', () => {
      const lowSeasonInput = {
        ...baseInput,
        patternData: {
          ...baseInput.patternData,
          revenue_patterns: {
            ...baseInput.patternData.revenue_patterns,
            seasonal_factor_average: 0.2 // Below low threshold (0.3)
          }
        }
      };

      const result = contextDetector.detectContext(lowSeasonInput);

      expect(result.currentContextObject.businessCycle).toBe('low_season');
      expect(result.contextReasons.businessCycle).toContain('Low seasonal activity');
    });

    test('should detect normal season correctly', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.currentContextObject.businessCycle).toBe('normal_season');
      expect(result.contextReasons.businessCycle).toContain('Normal seasonal activity');
    });
  });

  describe('User Behavior Context Detection', () => {
    test('should detect stress mode with high activity', () => {
      const stressInput = {
        ...baseInput,
        recentUserActivityLog: Array.from({ length: 25 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 60000).toISOString(), // Last 25 minutes
          action: `action_${i}`,
          user_id: 'user1'
        }))
      };

      const result = contextDetector.detectContext(stressInput);

      expect(result.currentContextObject.userBehavior).toBe('stress_mode');
      expect(result.contextReasons.userBehavior).toContain('High activity detected');
    });

    test('should detect stress mode with high error rate', () => {
      const errorInput = {
        ...baseInput,
        recentUserActivityLog: [
          {
            timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
            action: 'error_occurred',
            user_id: 'user1'
          },
          {
            timestamp: new Date(Date.now() - 10 * 60000).toISOString(),
            action: 'failed_action',
            user_id: 'user1'
          },
          {
            timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
            action: 'normal_action',
            user_id: 'user1'
          }
        ]
      };

      const result = contextDetector.detectContext(errorInput);

      expect(result.currentContextObject.userBehavior).toBe('stress_mode');
      expect(result.contextReasons.userBehavior).toContain('error rate');
    });

    test('should detect growth mode with analysis actions', () => {
      const growthInput = {
        ...baseInput,
        recentUserActivityLog: [
          {
            timestamp: new Date(Date.now() - 5 * 60000).toISOString(),
            action: 'view_report',
            user_id: 'user1'
          },
          {
            timestamp: new Date(Date.now() - 10 * 60000).toISOString(),
            action: 'analyze_patterns',
            user_id: 'user1'
          },
          {
            timestamp: new Date(Date.now() - 15 * 60000).toISOString(),
            action: 'view_report',
            user_id: 'user1'
          },
          {
            timestamp: new Date(Date.now() - 20 * 60000).toISOString(),
            action: 'analyze_customers',
            user_id: 'user1'
          },
          {
            timestamp: new Date(Date.now() - 25 * 60000).toISOString(),
            action: 'bulk_export',
            user_id: 'user1'
          }
        ]
      };

      const result = contextDetector.detectContext(growthInput);

      expect(result.currentContextObject.userBehavior).toBe('growth_mode');
      expect(result.contextReasons.userBehavior).toContain('Growth-focused activity');
    });

    test('should detect normal mode with regular activity', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.currentContextObject.userBehavior).toBe('normal_mode');
      expect(result.contextReasons.userBehavior).toContain('Normal activity level');
    });
  });

  describe('Data Quality Context Detection', () => {
    test('should detect high confidence with good data quality', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.currentContextObject.dataQuality).toBe('high_confidence');
      expect(result.dataQualityScore).toBeGreaterThan(85);
    });

    test('should detect low confidence with poor data quality', () => {
      const lowQualityInput = {
        ...baseInput,
        dataCompletenessMetrics: {
          ...baseInput.dataCompletenessMetrics,
          transactions: {
            ...baseInput.dataCompletenessMetrics.transactions,
            completeness_percentage: 40,
            last_updated: '2024-01-10T00:00:00Z' // 5 days old
          },
          patterns: {
            ...baseInput.dataCompletenessMetrics.patterns,
            average_confidence_score: 0.3,
            last_calculated: '2024-01-01T00:00:00Z' // 2 weeks old
          }
        }
      };

      const result = contextDetector.detectContext(lowQualityInput);

      expect(result.currentContextObject.dataQuality).toBe('low_confidence');
      expect(result.dataQualityScore).toBeLessThan(60);
    });

    test('should detect medium confidence with moderate data quality', () => {
      const mediumQualityInput = {
        ...baseInput,
        dataCompletenessMetrics: {
          ...baseInput.dataCompletenessMetrics,
          transactions: {
            ...baseInput.dataCompletenessMetrics.transactions,
            completeness_percentage: 75
          },
          patterns: {
            ...baseInput.dataCompletenessMetrics.patterns,
            average_confidence_score: 0.65
          }
        }
      };

      const result = contextDetector.detectContext(mediumQualityInput);

      expect(result.currentContextObject.dataQuality).toBe('medium_confidence');
      expect(result.dataQualityScore).toBeGreaterThanOrEqual(60);
      expect(result.dataQualityScore).toBeLessThan(85);
    });
  });

  describe('Dashboard Mode Recommendations', () => {
    test('should recommend maintenance mode for low data quality', () => {
      const lowQualityInput = {
        ...baseInput,
        dataCompletenessMetrics: {
          ...baseInput.dataCompletenessMetrics,
          transactions: {
            ...baseInput.dataCompletenessMetrics.transactions,
            completeness_percentage: 40
          }
        }
      };

      const result = contextDetector.detectContext(lowQualityInput);

      expect(result.recommendedDashboardMode).toBe('maintenance_mode');
    });

    test('should recommend alert mode for stress behavior', () => {
      const stressInput = {
        ...baseInput,
        recentUserActivityLog: Array.from({ length: 25 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 60000).toISOString(),
          action: `action_${i}`,
          user_id: 'user1'
        }))
      };

      const result = contextDetector.detectContext(stressInput);

      expect(result.recommendedDashboardMode).toBe('alert_mode');
    });

    test('should recommend growth analysis for growth mode', () => {
      const growthInput = {
        ...baseInput,
        recentUserActivityLog: Array.from({ length: 6 }, (_, i) => ({
          timestamp: new Date(Date.now() - i * 60000).toISOString(),
          action: 'view_report',
          user_id: 'user1'
        }))
      };

      const result = contextDetector.detectContext(growthInput);

      expect(result.recommendedDashboardMode).toBe('growth_analysis');
    });

    test('should recommend strategic planning for planning time', () => {
      const planningInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-14T10:00:00Z') // Sunday
      };

      const result = contextDetector.detectContext(planningInput);

      expect(result.recommendedDashboardMode).toBe('strategic_planning');
    });

    test('should recommend operational overview for normal context', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.recommendedDashboardMode).toBe('operational_overview');
    });
  });

  describe('Priority Insights Generation', () => {
    test('should prioritize data quality improvement for low confidence', () => {
      const lowQualityInput = {
        ...baseInput,
        dataCompletenessMetrics: {
          ...baseInput.dataCompletenessMetrics,
          transactions: {
            ...baseInput.dataCompletenessMetrics.transactions,
            completeness_percentage: 40
          }
        }
      };

      const result = contextDetector.detectContext(lowQualityInput);

      expect(result.priorityInsightsList).toContain('data_quality_improvement');
    });

    test('should include morning-specific insights', () => {
      const morningInput = {
        ...baseInput,
        currentTimestamp: new Date('2024-01-15T09:00:00Z')
      };

      const result = contextDetector.detectContext(morningInput);

      expect(result.priorityInsightsList).toContain('daily_sales_target');
    });

    test('should include material restock alerts when stock alerts exist', () => {
      const result = contextDetector.detectContext(baseInput);

      // baseInput has stock_alerts_count: 2
      expect(result.priorityInsightsList).toContain('material_restock_alert');
    });

    test('should include peak season insights', () => {
      const peakInput = {
        ...baseInput,
        patternData: {
          ...baseInput.patternData,
          revenue_patterns: {
            ...baseInput.patternData.revenue_patterns,
            seasonal_factor_average: 0.8
          }
        }
      };

      const result = contextDetector.detectContext(peakInput);

      expect(result.priorityInsightsList).toContain('inventory_optimization');
    });

    test('should limit insights to maximum of 5', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.priorityInsightsList.length).toBeLessThanOrEqual(5);
    });

    test('should remove duplicate insights', () => {
      const result = contextDetector.detectContext(baseInput);

      const uniqueInsights = [...new Set(result.priorityInsightsList)];
      expect(result.priorityInsightsList.length).toBe(uniqueInsights.length);
    });
  });

  describe('Context Confidence Calculation', () => {
    test('should calculate reasonable confidence scores', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.contextConfidence).toBeGreaterThan(0);
      expect(result.contextConfidence).toBeLessThanOrEqual(1);
    });

    test('should have lower confidence with poor data quality', () => {
      const lowQualityInput = {
        ...baseInput,
        dataCompletenessMetrics: {
          ...baseInput.dataCompletenessMetrics,
          transactions: {
            ...baseInput.dataCompletenessMetrics.transactions,
            completeness_percentage: 30
          }
        }
      };

      const lowQualityResult = contextDetector.detectContext(lowQualityInput);
      const normalResult = contextDetector.detectContext(baseInput);

      expect(lowQualityResult.contextConfidence).toBeLessThan(normalResult.contextConfidence);
    });
  });

  describe('Context Reasons', () => {
    test('should provide meaningful context reasons', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.contextReasons.time).toBeTruthy();
      expect(result.contextReasons.businessCycle).toBeTruthy();
      expect(result.contextReasons.userBehavior).toBeTruthy();
      expect(result.contextReasons.dataQuality).toBeTruthy();
    });

    test('should include specific metrics in reasons', () => {
      const result = contextDetector.detectContext(baseInput);

      expect(result.contextReasons.businessCycle).toContain('0.60');
      expect(result.contextReasons.dataQuality).toContain('%');
    });
  });

  describe('Edge Cases', () => {
    test('should handle empty user activity log', () => {
      const emptyActivityInput = {
        ...baseInput,
        recentUserActivityLog: []
      };

      const result = contextDetector.detectContext(emptyActivityInput);

      expect(result.currentContextObject.userBehavior).toBe('normal_mode');
      expect(result.contextConfidence).toBeGreaterThan(0);
    });

    test('should handle very old data timestamps', () => {
      const oldDataInput = {
        ...baseInput,
        dataCompletenessMetrics: {
          ...baseInput.dataCompletenessMetrics,
          transactions: {
            ...baseInput.dataCompletenessMetrics.transactions,
            last_updated: '2023-01-01T00:00:00Z' // Very old
          }
        }
      };

      const result = contextDetector.detectContext(oldDataInput);

      expect(result.currentContextObject.dataQuality).toBe('low_confidence');
    });

    test('should handle extreme seasonal factors', () => {
      const extremeSeasonalInput = {
        ...baseInput,
        patternData: {
          ...baseInput.patternData,
          revenue_patterns: {
            ...baseInput.patternData.revenue_patterns,
            seasonal_factor_average: 1.5 // Above normal range
          }
        }
      };

      const result = contextDetector.detectContext(extremeSeasonalInput);

      expect(result.currentContextObject.businessCycle).toBe('peak_season');
    });
  });
});
