/**
 * Progressive Disclosure Engine Unit Tests
 * 
 * Testing for layered information revelation system
 */

import { ProgressiveDisclosureEngine } from '../../src/lib/contextual-intelligence/engines/progressive-disclosure';
import { GeneratedInsight } from '../../src/lib/contextual-intelligence/types/insight-types';

describe('ProgressiveDisclosureEngine', () => {
  let engine: ProgressiveDisclosureEngine;
  let sampleInsight: GeneratedInsight;

  beforeEach(() => {
    engine = new ProgressiveDisclosureEngine();
    
    sampleInsight = {
      id: 'test_insight_001',
      priority: 'high',
      category: 'revenue_target',
      title: 'Capai Target Penjualan Harian Rp500,000',
      context: 'Pagi adalah waktu krusial untuk mengatur fokus penjualan. Berdasarkan pola historis, target harian Anda adalah Rp500,000.',
      action: '<PERSON>ikan kasir siap, monitor transaksi pembuka, dan promosikan paket "Cuci Hemat Pagi".',
      impact: 'Mencapai target awal hari memotivasi tim dan memastikan aliran kas yang stabil sepanjang hari.',
      timeEstimate: '15 mins',
      relatedData: {
        daily_target: 500000,
        current_revenue: 150000,
        revenue_percentage: 30
      },
      confidence: 0.85,
      tags: ['morning', 'revenue', 'target']
    };
  });

  describe('Progressive Insight Creation', () => {
    test('should create progressive insight with correct structure', () => {
      const progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      expect(progressiveInsight.id).toBe(sampleInsight.id);
      expect(progressiveInsight.layers).toHaveLength(5);
      expect(progressiveInsight.currentLevel).toBe(0);
      expect(progressiveInsight.maxLevel).toBe(4);
      expect(progressiveInsight.userInteractions).toBeDefined();
    });

    test('should create layers with correct visibility', () => {
      const progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      // Only headline should be visible initially
      expect(progressiveInsight.layers[0].isVisible).toBe(true);
      expect(progressiveInsight.layers[1].isVisible).toBe(false);
      expect(progressiveInsight.layers[2].isVisible).toBe(false);
      expect(progressiveInsight.layers[3].isVisible).toBe(false);
      expect(progressiveInsight.layers[4].isVisible).toBe(false);
    });

    test('should create headline with emoji and formatting', () => {
      const progressiveInsight = engine.createProgressiveInsight(sampleInsight);
      const headline = progressiveInsight.layers[0].content;

      expect(headline).toContain('🔥'); // High priority emoji
      expect(headline).toContain('💰'); // Revenue category emoji
      expect(headline).toContain(sampleInsight.title);
    });

    test('should include all insight content in layers', () => {
      const progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      const contextLayer = progressiveInsight.layers.find(l => l.level === 'context');
      const actionLayer = progressiveInsight.layers.find(l => l.level === 'action');
      const impactLayer = progressiveInsight.layers.find(l => l.level === 'impact');

      expect(contextLayer?.content).toBe(sampleInsight.context);
      expect(actionLayer?.content).toBe(sampleInsight.action);
      expect(impactLayer?.content).toBe(sampleInsight.impact);
    });
  });

  describe('Layer Revelation', () => {
    test('should reveal next layer correctly', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      // Reveal context layer
      progressiveInsight = engine.revealNextLayer(progressiveInsight);

      expect(progressiveInsight.currentLevel).toBe(1);
      expect(progressiveInsight.layers[1].isVisible).toBe(true);
      expect(progressiveInsight.userInteractions.expansions).toBe(1);
    });

    test('should not exceed maximum level', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      // Reveal all layers
      for (let i = 0; i < 10; i++) {
        progressiveInsight = engine.revealNextLayer(progressiveInsight);
      }

      expect(progressiveInsight.currentLevel).toBe(progressiveInsight.maxLevel);
      expect(progressiveInsight.currentLevel).toBe(4);
    });

    test('should track expansion interactions', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);
      const initialExpansions = progressiveInsight.userInteractions.expansions;

      progressiveInsight = engine.revealNextLayer(progressiveInsight);
      progressiveInsight = engine.revealNextLayer(progressiveInsight);

      expect(progressiveInsight.userInteractions.expansions).toBe(initialExpansions + 2);
    });
  });

  describe('Interaction Recording', () => {
    test('should record view interactions', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);
      const initialViews = progressiveInsight.userInteractions.views;

      progressiveInsight = engine.recordInteraction(progressiveInsight, 'view');

      expect(progressiveInsight.userInteractions.views).toBe(initialViews + 1);
      expect(progressiveInsight.userInteractions.last_viewed).toBeInstanceOf(Date);
    });

    test('should record expand interactions', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);
      const initialExpansions = progressiveInsight.userInteractions.expansions;

      progressiveInsight = engine.recordInteraction(progressiveInsight, 'expand');

      expect(progressiveInsight.userInteractions.expansions).toBe(initialExpansions + 1);
    });

    test('should record action interactions', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);
      const initialActions = progressiveInsight.userInteractions.actions_taken;

      progressiveInsight = engine.recordInteraction(progressiveInsight, 'action');

      expect(progressiveInsight.userInteractions.actions_taken).toBe(initialActions + 1);
    });
  });

  describe('Content Visibility', () => {
    test('should return only visible content', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      // Initially only headline visible
      let visibleContent = engine.getVisibleContent(progressiveInsight);
      expect(visibleContent).toHaveLength(1);
      expect(visibleContent[0].level).toBe('headline');

      // Reveal next layer
      progressiveInsight = engine.revealNextLayer(progressiveInsight);
      visibleContent = engine.getVisibleContent(progressiveInsight);
      expect(visibleContent).toHaveLength(2);
    });

    test('should correctly identify if more content is available', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      expect(engine.hasMoreContent(progressiveInsight)).toBe(true);

      // Reveal all layers
      while (engine.hasMoreContent(progressiveInsight)) {
        progressiveInsight = engine.revealNextLayer(progressiveInsight);
      }

      expect(engine.hasMoreContent(progressiveInsight)).toBe(false);
    });
  });

  describe('Engagement Scoring', () => {
    test('should calculate engagement score correctly', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      // No interactions initially
      let score = engine.getEngagementScore(progressiveInsight);
      expect(score).toBe(0);

      // Add some interactions
      progressiveInsight = engine.recordInteraction(progressiveInsight, 'view');
      progressiveInsight = engine.recordInteraction(progressiveInsight, 'expand');
      progressiveInsight = engine.recordInteraction(progressiveInsight, 'action');

      score = engine.getEngagementScore(progressiveInsight);
      expect(score).toBeGreaterThan(0);
      expect(score).toBeLessThanOrEqual(1);
    });

    test('should cap engagement score at maximum', () => {
      let progressiveInsight = engine.createProgressiveInsight(sampleInsight);

      // Add many interactions
      for (let i = 0; i < 10; i++) {
        progressiveInsight = engine.recordInteraction(progressiveInsight, 'view');
        progressiveInsight = engine.recordInteraction(progressiveInsight, 'expand');
        progressiveInsight = engine.recordInteraction(progressiveInsight, 'action');
      }

      const score = engine.getEngagementScore(progressiveInsight);
      expect(score).toBeLessThanOrEqual(1);
    });
  });

  describe('Analytics Summary', () => {
    test('should generate interaction summary for multiple insights', () => {
      const insights = [
        engine.createProgressiveInsight(sampleInsight),
        engine.createProgressiveInsight({
          ...sampleInsight,
          id: 'test_insight_002',
          category: 'inventory_management'
        }),
        engine.createProgressiveInsight({
          ...sampleInsight,
          id: 'test_insight_003',
          category: 'customer_service'
        })
      ];

      // Add some interactions
      insights[0] = engine.recordInteraction(insights[0], 'view');
      insights[0] = engine.revealNextLayer(insights[0]);
      insights[1] = engine.recordInteraction(insights[1], 'action');

      const summary = engine.generateInteractionSummary(insights);

      expect(summary.totalInsights).toBe(3);
      expect(summary.averageEngagement).toBeGreaterThanOrEqual(0);
      expect(summary.mostEngagedCategories).toBeInstanceOf(Array);
      expect(summary.completionRate).toBeGreaterThanOrEqual(0);
      expect(summary.completionRate).toBeLessThanOrEqual(1);
    });

    test('should handle empty insights array', () => {
      const summary = engine.generateInteractionSummary([]);

      expect(summary.totalInsights).toBe(0);
      expect(summary.averageEngagement).toBe(0);
      expect(summary.completionRate).toBe(0);
      expect(summary.mostEngagedCategories).toBeInstanceOf(Array);
    });
  });

  describe('Different Insight Categories', () => {
    test('should handle different priority levels', () => {
      const lowPriorityInsight = {
        ...sampleInsight,
        priority: 'low' as const
      };

      const progressiveInsight = engine.createProgressiveInsight(lowPriorityInsight);
      const headline = progressiveInsight.layers[0].content;

      expect(headline).toContain('💡'); // Low priority emoji
    });

    test('should handle different categories', () => {
      const inventoryInsight = {
        ...sampleInsight,
        category: 'inventory_management' as const
      };

      const progressiveInsight = engine.createProgressiveInsight(inventoryInsight);
      const headline = progressiveInsight.layers[0].content;

      expect(headline).toContain('📦'); // Inventory category emoji
    });

    test('should create detailed analysis layer', () => {
      const progressiveInsight = engine.createProgressiveInsight(sampleInsight);
      const detailsLayer = progressiveInsight.layers.find(l => l.level === 'details');

      expect(detailsLayer).toBeDefined();
      expect(detailsLayer?.content).toContain('Analisis Mendalam');
      expect(detailsLayer?.content).toContain('Tingkat Kepercayaan');
      expect(detailsLayer?.content).toContain('85%'); // Confidence percentage
    });
  });

  describe('Business Value Calculation', () => {
    test('should calculate business value for different categories', () => {
      const revenueInsight = engine.createProgressiveInsight({
        ...sampleInsight,
        category: 'revenue_target',
        priority: 'high'
      });

      const costInsight = engine.createProgressiveInsight({
        ...sampleInsight,
        category: 'cost_optimization',
        priority: 'medium'
      });

      const revenueImpactLayer = revenueInsight.layers.find(l => l.level === 'impact');
      const costImpactLayer = costInsight.layers.find(l => l.level === 'impact');

      expect(revenueImpactLayer?.data?.businessValue).toBeGreaterThan(0);
      expect(costImpactLayer?.data?.businessValue).toBeGreaterThan(0);
    });
  });
});
