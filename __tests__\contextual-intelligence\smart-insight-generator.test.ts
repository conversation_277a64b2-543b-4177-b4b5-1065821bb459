/**
 * Smart Insight Generator Unit Tests
 *
 * Comprehensive testing for the Smart Insight Generation Engine
 */

import { SmartInsightGenerator } from '../../src/lib/contextual-intelligence/engines/smart-insight-generator';
import {
  InsightGenerationInput,
  RealTimeData
} from '../../src/lib/contextual-intelligence/types/insight-types';
import { CurrentContextObject, PatternData } from '../../src/lib/contextual-intelligence/types';

describe('SmartInsightGenerator', () => {
  let generator: SmartInsightGenerator;
  let baseInput: InsightGenerationInput;

  beforeEach(() => {
    generator = new SmartInsightGenerator();

    // Create comprehensive test input
    baseInput = {
      contextObject: {
        time: 'morning',
        businessCycle: 'normal_season',
        userBehavior: 'normal_mode',
        dataQuality: 'high_confidence'
      } as CurrentContextObject,
      patternData: {
        customer_patterns: {
          total_analyzed: 80,
          high_confidence_count: 65,
          average_loyalty_score: 0.75,
          average_frequency_score: 0.68,
          seasonal_variance_average: 0.3
        },
        material_patterns: {
          total_analyzed: 18,
          reorder_recommendations_count: 3,
          average_efficiency_score: 0.82,
          stock_alerts_count: 2
        },
        revenue_patterns: {
          peak_days_count: 8,
          growth_trend: 'increasing',
          seasonal_factor_average: 0.6,
          revenue_volatility: 0.15
        }
      } as PatternData,
      realTimeData: {
        transactions: {
          today_count: 15,
          today_revenue: 750000,
          current_hour_count: 3,
          average_transaction_value: 50000,
          peak_hour_today: 10
        },
        materials: [
          {
            material_id: 'det001',
            material_name: 'Deterjen Premium',
            current_stock: 25,
            minimum_threshold: 20,
            category: 'DETERGENT',
            last_restock_date: '2024-01-10',
            usage_rate_per_day: 10,
            days_until_empty: 2.5
          },
          {
            material_id: 'sof001',
            material_name: 'Pelembut Pakaian',
            current_stock: 50,
            minimum_threshold: 15,
            category: 'SOFTENER',
            last_restock_date: '2024-01-12',
            usage_rate_per_day: 5,
            days_until_empty: 10
          }
        ],
        customers: {
          new_customers_today: 5,
          returning_customers_today: 10,
          pending_orders: 3,
          overdue_pickups: 1
        },
        operations: {
          active_machines: 8,
          total_machines: 10,
          queue_length: 4,
          estimated_completion_times: [30, 45, 60, 90],
          staff_on_duty: 3
        }
      } as RealTimeData,
      timestamp: new Date('2024-01-15T09:30:00Z')
    };
  });

  describe('Insight Generation', () => {
    test('should generate insights for morning context', () => {
      const result = generator.generateInsights(baseInput);

      expect(result.insights).toBeDefined();
      expect(result.insights.length).toBeGreaterThan(0);
      expect(result.totalGenerated).toBe(result.insights.length);
      expect(result.generationTimestamp).toBeInstanceOf(Date);
    });

    test('should prioritize high priority insights', () => {
      const result = generator.generateInsights(baseInput);

      const highPriorityInsights = result.insights.filter(i => i.priority === 'high');
      const mediumPriorityInsights = result.insights.filter(i => i.priority === 'medium');

      // High priority insights should appear first
      if (highPriorityInsights.length > 0 && mediumPriorityInsights.length > 0) {
        const firstHighIndex = result.insights.findIndex(i => i.priority === 'high');
        const firstMediumIndex = result.insights.findIndex(i => i.priority === 'medium');
        expect(firstHighIndex).toBeLessThan(firstMediumIndex);
      }
    });

    test('should generate stock alert for low stock material', () => {
      const result = generator.generateInsights(baseInput);

      const stockAlerts = result.insights.filter(insight =>
        insight.category === 'inventory_management' &&
        insight.title.includes('Deterjen Premium')
      );

      expect(stockAlerts.length).toBeGreaterThan(0);
      expect(stockAlerts[0].priority).toBe('high');
    });

    test('should generate daily target insight for morning context', () => {
      const result = generator.generateInsights(baseInput);

      const targetInsights = result.insights.filter(insight =>
        insight.category === 'revenue_target' &&
        insight.title.includes('Target')
      );

      expect(targetInsights.length).toBeGreaterThan(0);
    });

    test('should respect maximum insights limit', () => {
      const customGenerator = new SmartInsightGenerator({
        maxInsightsPerGeneration: 3
      });

      const result = customGenerator.generateInsights(baseInput);

      expect(result.insights.length).toBeLessThanOrEqual(3);
    });
  });

  describe('Context-Specific Insights', () => {
    test('should generate midday insights for midday context', () => {
      const middayInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          time: 'midday'
        }
      };

      const result = generator.generateInsights(middayInput);

      const operationalInsights = result.insights.filter(insight =>
        insight.category === 'operational_efficiency'
      );

      expect(operationalInsights.length).toBeGreaterThan(0);
    });

    test('should generate evening insights for evening context', () => {
      const eveningInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          time: 'evening'
        }
      };

      const result = generator.generateInsights(eveningInput);

      const customerInsights = result.insights.filter(insight =>
        insight.category === 'customer_service'
      );

      expect(customerInsights.length).toBeGreaterThan(0);
    });

    test('should generate planning insights for planning context', () => {
      const planningInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          time: 'planning'
        }
      };

      const result = generator.generateInsights(planningInput);

      const growthInsights = result.insights.filter(insight =>
        insight.category === 'growth_opportunity'
      );

      expect(growthInsights.length).toBeGreaterThan(0);
    });
  });

  describe('Stress Mode Insights', () => {
    test('should generate urgent insights for stress mode', () => {
      const stressInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          userBehavior: 'stress_mode',
          dataQuality: 'low_confidence'
        }
      };

      const result = generator.generateInsights(stressInput);

      const urgentInsights = result.insights.filter(insight =>
        insight.priority === 'high' &&
        (insight.category === 'data_quality' || insight.title.includes('URGENT'))
      );

      expect(urgentInsights.length).toBeGreaterThan(0);
    });

    test('should prioritize critical stock alerts in stress mode', () => {
      const criticalStockInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          userBehavior: 'stress_mode'
        },
        realTimeData: {
          ...baseInput.realTimeData,
          materials: [
            {
              material_id: 'det001',
              material_name: 'Deterjen Premium',
              current_stock: 5,
              minimum_threshold: 20,
              category: 'DETERGENT',
              last_restock_date: '2024-01-10',
              usage_rate_per_day: 10,
              days_until_empty: 0.5
            }
          ]
        }
      };

      const result = generator.generateInsights(criticalStockInput);

      const criticalAlerts = result.insights.filter(insight =>
        insight.title.includes('URGENT') || insight.title.includes('Hampir Habis')
      );

      expect(criticalAlerts.length).toBeGreaterThan(0);
      expect(criticalAlerts[0].priority).toBe('high');
    });
  });

  describe('Business Cycle Insights', () => {
    test('should generate peak season insights', () => {
      const peakSeasonInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          businessCycle: 'peak_season'
        },
        patternData: {
          ...baseInput.patternData,
          revenue_patterns: {
            ...baseInput.patternData.revenue_patterns,
            seasonal_factor_average: 0.8
          }
        }
      };

      const result = generator.generateInsights(peakSeasonInput);

      const seasonalInsights = result.insights.filter(insight =>
        insight.tags.includes('seasonal') ||
        insight.category === 'operational_efficiency'
      );

      expect(seasonalInsights.length).toBeGreaterThan(0);
    });

    test('should generate low season insights', () => {
      const lowSeasonInput = {
        ...baseInput,
        contextObject: {
          ...baseInput.contextObject,
          businessCycle: 'low_season'
        },
        patternData: {
          ...baseInput.patternData,
          revenue_patterns: {
            ...baseInput.patternData.revenue_patterns,
            seasonal_factor_average: 0.2
          }
        }
      };

      const result = generator.generateInsights(lowSeasonInput);

      const costOptimizationInsights = result.insights.filter(insight =>
        insight.category === 'cost_optimization'
      );

      expect(costOptimizationInsights.length).toBeGreaterThan(0);
    });
  });

  describe('Template Variable Interpolation', () => {
    test('should correctly interpolate currency values', () => {
      const result = generator.generateInsights(baseInput);

      const revenueInsights = result.insights.filter(insight =>
        insight.title.includes('Rp') || insight.context.includes('Rp')
      );

      expect(revenueInsights.length).toBeGreaterThan(0);

      // Check if currency formatting is applied
      revenueInsights.forEach(insight => {
        if (insight.title.includes('Rp')) {
          expect(insight.title).toMatch(/Rp[\d,]+/);
        }
      });
    });

    test('should correctly interpolate material information', () => {
      const result = generator.generateInsights(baseInput);

      const materialInsights = result.insights.filter(insight =>
        insight.category === 'inventory_management'
      );

      expect(materialInsights.length).toBeGreaterThan(0);

      materialInsights.forEach(insight => {
        expect(insight.relatedData).toBeDefined();
        expect(insight.relatedData.material_name).toBeDefined();
      });
    });
  });

  describe('Insight Quality', () => {
    test('should generate insights with required fields', () => {
      const result = generator.generateInsights(baseInput);

      result.insights.forEach(insight => {
        expect(insight.id).toBeDefined();
        expect(insight.priority).toMatch(/^(high|medium|low)$/);
        expect(insight.category).toBeDefined();
        expect(insight.title).toBeDefined();
        expect(insight.context).toBeDefined();
        expect(insight.action).toBeDefined();
        expect(insight.impact).toBeDefined();
        expect(insight.timeEstimate).toBeDefined();
        expect(insight.confidence).toBeGreaterThan(0);
        expect(insight.confidence).toBeLessThanOrEqual(1);
        expect(insight.tags).toBeInstanceOf(Array);
      });
    });

    test('should generate actionable insights', () => {
      const result = generator.generateInsights(baseInput);

      result.insights.forEach(insight => {
        // Action should contain actionable verbs
        const actionableVerbs = ['pastikan', 'periksa', 'hubungi', 'monitor', 'aktifkan', 'informasikan', 'evaluasi'];
        const hasActionableVerb = actionableVerbs.some(verb =>
          insight.action.toLowerCase().includes(verb)
        );
        expect(hasActionableVerb).toBe(true);
      });
    });

    test('should provide realistic time estimates', () => {
      const result = generator.generateInsights(baseInput);

      const validTimeEstimates = ['5 mins', '10 mins', '15 mins', '20 mins', '30 mins', '45 mins', '1 hour', '2 hours'];

      result.insights.forEach(insight => {
        expect(validTimeEstimates).toContain(insight.timeEstimate);
      });
    });
  });

  describe('Context Summary', () => {
    test('should generate accurate context summary', () => {
      const result = generator.generateInsights(baseInput);

      expect(result.contextSummary).toBeDefined();
      expect(result.contextSummary.primaryContext).toContain('morning');
      expect(result.contextSummary.dataQuality).toBe('high_confidence');
      expect(result.contextSummary.secondaryFactors).toBeInstanceOf(Array);
      expect(result.contextSummary.secondaryFactors.length).toBeGreaterThan(0);
    });

    test('should count insights by priority correctly', () => {
      const result = generator.generateInsights(baseInput);

      const actualHighCount = result.insights.filter(i => i.priority === 'high').length;
      const actualMediumCount = result.insights.filter(i => i.priority === 'medium').length;
      const actualLowCount = result.insights.filter(i => i.priority === 'low').length;

      expect(result.highPriorityCount).toBe(actualHighCount);
      expect(result.mediumPriorityCount).toBe(actualMediumCount);
      expect(result.lowPriorityCount).toBe(actualLowCount);
    });
  });
});
