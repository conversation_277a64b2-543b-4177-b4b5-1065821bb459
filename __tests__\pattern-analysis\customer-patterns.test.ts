import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { PrismaClient, ServiceType, DayType, TimeOfDay, WeatherContext } from '@/generated/prisma';
import { CustomerPatternAnalyzer } from '@/lib/pattern-analysis/customer-patterns';

const prisma = new PrismaClient();
const analyzer = new CustomerPatternAnalyzer();

describe('CustomerPatternAnalyzer', () => {
  let testCustomerId: string;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.transaction.deleteMany({
      where: {
        customer: {
          phone_number: {
            startsWith: '081999'
          }
        }
      }
    });
    
    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081999'
        }
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.transaction.deleteMany({
      where: {
        customer: {
          phone_number: {
            startsWith: '081999'
          }
        }
      }
    });
    
    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081999'
        }
      }
    });
    
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create test customer
    const customer = await prisma.customer.create({
      data: {
        name: 'Test Customer Pattern',
        phone_number: '081999888777',
        email: '<EMAIL>',
        registration_date: new Date('2024-01-01')
      }
    });
    testCustomerId = customer.id;

    // Create test transactions with patterns
    const transactions = [
      {
        customer_id: testCustomerId,
        service_type: ServiceType.CUCI_SETRIKA,
        weight_kg: 3.0,
        price: 30000,
        transaction_date: new Date('2024-01-15'),
        context_day_type: DayType.WEEKDAY,
        context_time_of_day: TimeOfDay.MORNING,
        context_weather: WeatherContext.SUNNY,
        context_seasonal_factor: 0.6
      },
      {
        customer_id: testCustomerId,
        service_type: ServiceType.CUCI_SETRIKA,
        weight_kg: 2.5,
        price: 25000,
        transaction_date: new Date('2024-02-15'),
        context_day_type: DayType.WEEKDAY,
        context_time_of_day: TimeOfDay.MORNING,
        context_weather: WeatherContext.SUNNY,
        context_seasonal_factor: 0.7
      },
      {
        customer_id: testCustomerId,
        service_type: ServiceType.CUCI_KERING,
        weight_kg: 4.0,
        price: 35000,
        transaction_date: new Date('2024-03-15'),
        context_day_type: DayType.WEEKEND,
        context_time_of_day: TimeOfDay.AFTERNOON,
        context_weather: WeatherContext.RAINY,
        context_seasonal_factor: 0.8
      },
      {
        customer_id: testCustomerId,
        service_type: ServiceType.CUCI_SETRIKA,
        weight_kg: 3.5,
        price: 32000,
        transaction_date: new Date('2024-04-15'),
        context_day_type: DayType.WEEKDAY,
        context_time_of_day: TimeOfDay.MORNING,
        context_weather: WeatherContext.SUNNY,
        context_seasonal_factor: 0.9
      }
    ];

    for (const transaction of transactions) {
      await prisma.transaction.create({ data: transaction });
    }
  });

  describe('analyzeCustomerPattern', () => {
    it('should analyze customer pattern with sufficient data', async () => {
      const pattern = await analyzer.analyzeCustomerPattern(testCustomerId);

      expect(pattern).toBeDefined();
      expect(pattern?.customer_id).toBe(testCustomerId);
      expect(pattern?.data_points_count).toBe(4);
      expect(pattern?.calculated_frequency).toBeGreaterThan(0);
      expect(pattern?.confidence_score).toBeGreaterThan(0);
    });

    it('should return null for customer with insufficient data', async () => {
      // Create customer with only 1 transaction
      const customer = await prisma.customer.create({
        data: {
          name: 'Insufficient Data Customer',
          phone_number: '081999777666',
          email: '<EMAIL>'
        }
      });

      await prisma.transaction.create({
        data: {
          customer_id: customer.id,
          service_type: ServiceType.CUCI_SAJA,
          weight_kg: 2.0,
          price: 20000,
          transaction_date: new Date()
        }
      });

      const pattern = await analyzer.analyzeCustomerPattern(customer.id);
      expect(pattern).toBeNull();
    });

    it('should calculate correct frequency metrics', async () => {
      const pattern = await analyzer.analyzeCustomerPattern(testCustomerId);

      expect(pattern).toBeDefined();
      expect(pattern?.calculated_frequency).toBeGreaterThan(0);
      expect(['increasing', 'decreasing', 'stable']).toContain(pattern?.frequency_trend);
      expect(pattern?.last_transaction_days_ago).toBeGreaterThanOrEqual(0);
    });

    it('should identify preferred service type', async () => {
      const pattern = await analyzer.analyzeCustomerPattern(testCustomerId);

      expect(pattern).toBeDefined();
      // Should prefer CUCI_SETRIKA (3 out of 4 transactions)
      expect(pattern?.preferred_service_type).toBe(ServiceType.CUCI_SETRIKA);
      expect(pattern?.preferred_service_confidence).toBeGreaterThan(0.5);
    });

    it('should calculate behavioral scores', async () => {
      const pattern = await analyzer.analyzeCustomerPattern(testCustomerId);

      expect(pattern).toBeDefined();
      expect(pattern?.loyalty_score).toBeGreaterThanOrEqual(0);
      expect(pattern?.loyalty_score).toBeLessThanOrEqual(1);
      expect(pattern?.value_score).toBeGreaterThanOrEqual(0);
      expect(pattern?.value_score).toBeLessThanOrEqual(1);
      expect(pattern?.predictability_score).toBeGreaterThanOrEqual(0);
      expect(pattern?.predictability_score).toBeLessThanOrEqual(1);
    });

    it('should calculate seasonal patterns', async () => {
      const pattern = await analyzer.analyzeCustomerPattern(testCustomerId);

      expect(pattern).toBeDefined();
      expect(pattern?.seasonal_activity_json).toBeDefined();
      expect(pattern?.peak_months).toBeDefined();
      expect(pattern?.low_months).toBeDefined();
      expect(pattern?.seasonal_variance).toBeGreaterThanOrEqual(0);
    });

    it('should calculate confidence score based on data quality', async () => {
      const pattern = await analyzer.analyzeCustomerPattern(testCustomerId);

      expect(pattern).toBeDefined();
      expect(pattern?.confidence_score).toBeGreaterThanOrEqual(0);
      expect(pattern?.confidence_score).toBeLessThanOrEqual(1);
      
      // With 4 transactions, confidence should be moderate
      expect(pattern?.confidence_score).toBeGreaterThan(0.1);
    });
  });

  describe('analyzeAllCustomers', () => {
    it('should analyze all customers with sufficient data', async () => {
      const patterns = await analyzer.analyzeAllCustomers();

      expect(Array.isArray(patterns)).toBe(true);
      expect(patterns.length).toBeGreaterThanOrEqual(1);
      
      const testPattern = patterns.find(p => p.customer_id === testCustomerId);
      expect(testPattern).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      // This test ensures the analyzer doesn't crash on problematic data
      const patterns = await analyzer.analyzeAllCustomers();
      
      expect(Array.isArray(patterns)).toBe(true);
      // Should not throw errors even if some customers have issues
    });
  });

  describe('edge cases', () => {
    it('should handle customer with no transactions', async () => {
      const customer = await prisma.customer.create({
        data: {
          name: 'No Transactions Customer',
          phone_number: '081999555444',
          email: '<EMAIL>'
        }
      });

      const pattern = await analyzer.analyzeCustomerPattern(customer.id);
      expect(pattern).toBeNull();
    });

    it('should handle non-existent customer', async () => {
      const pattern = await analyzer.analyzeCustomerPattern('non-existent-id');
      expect(pattern).toBeNull();
    });

    it('should handle customer with transactions outside analysis period', async () => {
      const customer = await prisma.customer.create({
        data: {
          name: 'Old Transactions Customer',
          phone_number: '081999333222',
          email: '<EMAIL>'
        }
      });

      // Create transaction from 2 years ago (outside analysis period)
      await prisma.transaction.create({
        data: {
          customer_id: customer.id,
          service_type: ServiceType.CUCI_SAJA,
          weight_kg: 2.0,
          price: 20000,
          transaction_date: new Date('2022-01-01')
        }
      });

      const pattern = await analyzer.analyzeCustomerPattern(customer.id);
      expect(pattern).toBeNull();
    });
  });
});
