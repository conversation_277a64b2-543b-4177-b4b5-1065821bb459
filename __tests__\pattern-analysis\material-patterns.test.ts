import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { PrismaClient, MaterialCategory, ServiceType, DayType, TimeOfDay } from '@/generated/prisma';
import { MaterialPatternAnalyzer } from '@/lib/pattern-analysis/material-patterns';

const prisma = new PrismaClient();
const analyzer = new MaterialPatternAnalyzer();

describe('MaterialPatternAnalyzer', () => {
  let testMaterialId: string;
  let testCustomerId: string;

  beforeAll(async () => {
    // Clean up any existing test data
    await prisma.transactionMaterial.deleteMany({
      where: {
        material: {
          material_name: {
            startsWith: 'Test Material Pattern'
          }
        }
      }
    });

    await prisma.materialInventory.deleteMany({
      where: {
        material_name: {
          startsWith: 'Test Material Pattern'
        }
      }
    });

    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081888'
        }
      }
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.transactionMaterial.deleteMany({
      where: {
        material: {
          material_name: {
            startsWith: 'Test Material Pattern'
          }
        }
      }
    });

    await prisma.materialInventory.deleteMany({
      where: {
        material_name: {
          startsWith: 'Test Material Pattern'
        }
      }
    });

    await prisma.customer.deleteMany({
      where: {
        phone_number: {
          startsWith: '081888'
        }
      }
    });

    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create test customer
    const customer = await prisma.customer.create({
      data: {
        name: 'Test Material Customer',
        phone_number: '081888777666',
        email: '<EMAIL>'
      }
    });
    testCustomerId = customer.id;

    // Create test material
    const material = await prisma.materialInventory.create({
      data: {
        material_name: 'Test Material Pattern Detergent',
        current_stock_unit: 100.0,
        unit_of_measure: 'liter',
        usage_rate_per_transaction: 0.2,
        usage_rate_per_kg: 0.1,
        minimum_stock_threshold: 20.0,
        cost_per_unit: 15000,
        category: MaterialCategory.DETERGENT
      }
    });
    testMaterialId = material.id;

    // Create test transactions with material usage
    const transactions = [];
    const usageRecords = [];

    for (let i = 0; i < 10; i++) {
      const transactionDate = new Date();
      transactionDate.setDate(transactionDate.getDate() - (i * 10)); // Every 10 days

      const transaction = await prisma.transaction.create({
        data: {
          customer_id: testCustomerId,
          service_type: ServiceType.CUCI_SETRIKA,
          weight_kg: 3.0 + (i * 0.5),
          price: 30000,
          transaction_date: transactionDate,
          context_day_type: i % 2 === 0 ? DayType.WEEKDAY : DayType.WEEKEND,
          context_time_of_day: i % 3 === 0 ? TimeOfDay.MORNING : TimeOfDay.AFTERNOON
        }
      });

      // Create material usage for this transaction
      await prisma.transactionMaterial.create({
        data: {
          transaction_id: transaction.id,
          material_id: testMaterialId,
          quantity_used: 0.3 + (i * 0.02), // Varying usage
          cost_at_time: 15000 * (0.3 + (i * 0.02))
        }
      });
    }
  });

  describe('analyzeMaterialPattern', () => {
    it('should analyze material pattern with sufficient data', async () => {
      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(pattern?.material_id).toBe(testMaterialId);
      expect(pattern?.data_points_count).toBe(10);
      expect(pattern?.average_consumption_rate).toBeGreaterThan(0);
      expect(pattern?.confidence_score).toBeGreaterThan(0);
    });

    it('should return null for material with insufficient data', async () => {
      // Create material with no usage
      const material = await prisma.materialInventory.create({
        data: {
          material_name: 'Test Material No Usage',
          current_stock_unit: 50.0,
          unit_of_measure: 'liter',
          category: MaterialCategory.DETERGENT
        }
      });

      const pattern = await analyzer.analyzeMaterialPattern(material.id);
      expect(pattern).toBeNull();
    });

    it('should calculate consumption metrics correctly', async () => {
      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(pattern?.average_consumption_rate).toBeGreaterThan(0);
      expect(['increasing', 'decreasing', 'stable']).toContain(pattern?.consumption_trend);
    });

    it('should identify usage patterns', async () => {
      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(Object.values(DayType)).toContain(pattern?.peak_usage_day_type);
      expect(Object.values(TimeOfDay)).toContain(pattern?.peak_usage_time);
    });

    it('should calculate efficiency metrics', async () => {
      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(pattern?.cost_efficiency_score).toBeGreaterThanOrEqual(0);
      expect(pattern?.cost_efficiency_score).toBeLessThanOrEqual(1);
      expect(pattern?.usage_efficiency_score).toBeGreaterThanOrEqual(0);
      expect(pattern?.usage_efficiency_score).toBeLessThanOrEqual(1);
      expect(pattern?.waste_indicator).toBeGreaterThanOrEqual(0);
    });

    it('should calculate stock predictions', async () => {
      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(pattern?.predicted_days_to_empty).toBeGreaterThanOrEqual(0);
      expect(typeof pattern?.reorder_recommendation).toBe('boolean');
      expect(pattern?.optimal_stock_level).toBeGreaterThan(0);
    });

    it('should calculate seasonal patterns', async () => {
      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(pattern?.seasonal_adjustment_json).toBeDefined();
      expect(pattern?.peak_consumption_months).toBeDefined();
      expect(pattern?.low_consumption_months).toBeDefined();
      expect(pattern?.seasonal_multiplier).toBeGreaterThan(0);
    });

    it('should recommend reorder when stock is low', async () => {
      // Update material to have very low stock
      await prisma.materialInventory.update({
        where: { id: testMaterialId },
        data: { current_stock_unit: 1.0 } // Very low stock
      });

      const pattern = await analyzer.analyzeMaterialPattern(testMaterialId);

      expect(pattern).toBeDefined();
      expect(pattern?.predicted_days_to_empty).toBeLessThan(30);
      expect(pattern?.reorder_recommendation).toBe(true);
    });
  });

  describe('analyzeAllMaterials', () => {
    it('should analyze all materials with sufficient data', async () => {
      const patterns = await analyzer.analyzeAllMaterials();

      expect(Array.isArray(patterns)).toBe(true);
      expect(patterns.length).toBeGreaterThanOrEqual(1);
      
      const testPattern = patterns.find(p => p.material_id === testMaterialId);
      expect(testPattern).toBeDefined();
    });

    it('should handle errors gracefully', async () => {
      const patterns = await analyzer.analyzeAllMaterials();
      
      expect(Array.isArray(patterns)).toBe(true);
      // Should not throw errors even if some materials have issues
    });
  });

  describe('edge cases', () => {
    it('should handle material with no usage records', async () => {
      const material = await prisma.materialInventory.create({
        data: {
          material_name: 'Test Material No Records',
          current_stock_unit: 50.0,
          unit_of_measure: 'liter',
          category: MaterialCategory.DETERGENT
        }
      });

      const pattern = await analyzer.analyzeMaterialPattern(material.id);
      expect(pattern).toBeNull();
    });

    it('should handle non-existent material', async () => {
      const pattern = await analyzer.analyzeMaterialPattern('non-existent-id');
      expect(pattern).toBeNull();
    });

    it('should handle material with usage outside analysis period', async () => {
      const material = await prisma.materialInventory.create({
        data: {
          material_name: 'Test Material Old Usage',
          current_stock_unit: 50.0,
          unit_of_measure: 'liter',
          category: MaterialCategory.DETERGENT
        }
      });

      // Create old transaction (outside analysis period)
      const oldTransaction = await prisma.transaction.create({
        data: {
          customer_id: testCustomerId,
          service_type: ServiceType.CUCI_SAJA,
          weight_kg: 2.0,
          price: 20000,
          transaction_date: new Date('2022-01-01') // Very old
        }
      });

      await prisma.transactionMaterial.create({
        data: {
          transaction_id: oldTransaction.id,
          material_id: material.id,
          quantity_used: 0.2,
          cost_at_time: 3000
        }
      });

      const pattern = await analyzer.analyzeMaterialPattern(material.id);
      expect(pattern).toBeNull();
    });

    it('should handle zero consumption rate gracefully', async () => {
      // This tests division by zero scenarios
      const material = await prisma.materialInventory.create({
        data: {
          material_name: 'Test Material Zero Usage',
          current_stock_unit: 1000.0, // Very high stock
          unit_of_measure: 'liter',
          category: MaterialCategory.DETERGENT
        }
      });

      // Create minimal usage
      const transaction = await prisma.transaction.create({
        data: {
          customer_id: testCustomerId,
          service_type: ServiceType.CUCI_SAJA,
          weight_kg: 1.0,
          price: 10000,
          transaction_date: new Date()
        }
      });

      await prisma.transactionMaterial.create({
        data: {
          transaction_id: transaction.id,
          material_id: material.id,
          quantity_used: 0.001, // Minimal usage
          cost_at_time: 15
        }
      });

      const pattern = await analyzer.analyzeMaterialPattern(material.id);
      
      // Should handle this gracefully without errors
      if (pattern) {
        expect(pattern.predicted_days_to_empty).toBeGreaterThan(0);
      }
    });
  });
});
