import React, { useState, useEffect } from 'react';
import { Clock, TrendingUp, AlertTriangle, CheckCircle, Eye, ChevronRight, Calendar, DollarSign, Package, Users, Zap } from 'lucide-react';

const LaundryDashboard = () => {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [expandedCard, setExpandedCard] = useState(null);
  const [contextMode, setContextMode] = useState('morning');

  useEffect(() => {
    const timer = setInterval(() => setCurrentTime(new Date()), 1000);
    return () => clearInterval(timer);
  }, []);

  // Determine context based on time
  useEffect(() => {
    const hour = currentTime.getHours();
    if (hour >= 7 && hour < 10) setContextMode('morning');
    else if (hour >= 10 && hour < 16) setContextMode('midday');
    else if (hour >= 17 && hour < 20) setContextMode('evening');
    else setContextMode('planning');
  }, [currentTime]);

  const contextConfig = {
    morning: {
      title: "Siap Operasional",
      subtitle: "3 hal yang perlu dihandle hari ini",
      color: "from-amber-500 to-orange-500",
      icon: <Zap className="w-5 h-5" />
    },
    midday: {
      title: "Monitoring Mode", 
      subtitle: "Semua berjalan normal",
      color: "from-blue-500 to-cyan-500",
      icon: <Eye className="w-5 h-5" />
    },
    evening: {
      title: "Review & Planning",
      subtitle: "Hari ini & persiapan besok",
      color: "from-purple-500 to-pink-500", 
      icon: <Calendar className="w-5 h-5" />
    },
    planning: {
      title: "Strategic Mode",
      subtitle: "Waktu untuk planning jangka panjang",
      color: "from-green-500 to-teal-500",
      icon: <TrendingUp className="w-5 h-5" />
    }
  };

  const currentConfig = contextConfig[contextMode];

  const getContextualInsights = () => {
    switch(contextMode) {
      case 'morning':
        return [
          {
            id: 'ops1',
            priority: 'high',
            title: 'Mesin 2 perlu dicek',
            context: 'Kemarin ada komplain bau tidak sedap',
            action: 'Cek filter dan drum sebelum operasional',
            impact: 'Mencegah komplain berulang',
            timeEstimate: '15 menit'
          },
          {
            id: 'ops2', 
            priority: 'medium',
            title: 'Stok deterjen premium',
            context: 'Cukup sampai sore (12 paket tersisa)',
            action: 'Order sekarang atau switch beberapa customer ke regular',
            impact: 'Hindari gangguan layanan premium',
            timeEstimate: '5 menit telepon'
          },
          {
            id: 'ops3',
            priority: 'low',
            title: '8 pickup terjadwal',
            context: 'Rute sudah optimal, 1 customer baru di Jl. Merdeka',
            action: 'Siapkan tas khusus untuk customer baru',
            impact: 'First impression yang baik',
            timeEstimate: '2 menit'
          }
        ];
      case 'evening':
        return [
          {
            id: 'rev1',
            priority: 'high',
            title: "Today's Wins",
            context: 'Revenue Rp 450K (12% di atas target), 0 komplain',
            action: 'Catat apa yang berbeda hari ini',
            impact: 'Replicate success pattern',
            timeEstimate: 'Refleksi 5 menit'
          },
          {
            id: 'rev2',
            priority: 'medium', 
            title: 'Besok perlu perhatian',
            context: 'Cuaca diprediksi hujan, biasanya order naik 30%',
            action: 'Siapkan stok ekstra dan perpanjang jam buka',
            impact: 'Maximize rainy day opportunity',
            timeEstimate: 'Prep 20 menit'
          }
        ];
      default:
        return [
          {
            id: 'def1',
            priority: 'medium',
            title: 'Operasional berjalan normal',
            context: 'Tidak ada alert kritikal saat ini',
            action: 'Monitor terus untuk anomali',
            impact: 'Maintain stability',
            timeEstimate: 'Ongoing'
          }
        ];
    }
  };

  const insights = getContextualInsights();

  const ProgressiveCard = ({ insight, isExpanded, onToggle }) => {
    const priorityColors = {
      high: 'border-l-red-500 bg-red-50',
      medium: 'border-l-yellow-500 bg-yellow-50', 
      low: 'border-l-green-500 bg-green-50'
    };

    return (
      <div className={`border-l-4 p-4 rounded-r-lg cursor-pointer transition-all duration-300 ${priorityColors[insight.priority]} ${isExpanded ? 'shadow-lg scale-102' : 'hover:shadow-md'}`}>
        <div onClick={() => onToggle(insight.id)} className="flex justify-between items-start">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">{insight.title}</h3>
            <p className="text-sm text-gray-600 mt-1">{insight.context}</p>
          </div>
          <ChevronRight className={`w-5 h-5 text-gray-400 transition-transform ${isExpanded ? 'rotate-90' : ''}`} />
        </div>
        
        {isExpanded && (
          <div className="mt-4 space-y-3 animate-fadeIn">
            <div className="bg-white p-3 rounded border">
              <h4 className="font-medium text-sm text-gray-700">Recommended Action:</h4>
              <p className="text-sm text-gray-900 mt-1">{insight.action}</p>
            </div>
            <div className="flex justify-between text-xs text-gray-500">
              <span>Impact: {insight.impact}</span>
              <span>Est. Time: {insight.timeEstimate}</span>
            </div>
            <button className="w-full bg-gray-900 text-white py-2 px-4 rounded text-sm hover:bg-gray-800 transition-colors">
              Take Action
            </button>
          </div>
        )}
      </div>
    );
  };

  const QuickMetrics = () => (
    <div className="grid grid-cols-4 gap-4 mb-6">
      {[
        { icon: <DollarSign className="w-5 h-5" />, label: 'Today Revenue', value: 'Rp 450K', trend: '+12%', positive: true },
        { icon: <Package className="w-5 h-5" />, label: 'Active Orders', value: '23', trend: 'Normal', positive: true },
        { icon: <Users className="w-5 h-5" />, label: 'Customers', value: '18', trend: '+2', positive: true },
        { icon: <AlertTriangle className="w-5 h-5" />, label: 'Alerts', value: '2', trend: 'Low priority', positive: false }
      ].map((metric, idx) => (
        <div key={idx} className="bg-white p-4 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div className={`p-2 rounded-lg ${metric.positive ? 'bg-green-100 text-green-600' : 'bg-yellow-100 text-yellow-600'}`}>
              {metric.icon}
            </div>
            <span className={`text-xs ${metric.positive ? 'text-green-600' : 'text-yellow-600'}`}>{metric.trend}</span>
          </div>
          <div className="mt-2">
            <div className="text-2xl font-bold text-gray-900">{metric.value}</div>
            <div className="text-sm text-gray-500">{metric.label}</div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      {/* Header with Context Awareness */}
      <div className={`bg-gradient-to-r ${currentConfig.color} text-white p-6 rounded-lg mb-6 shadow-lg`}>
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              {currentConfig.icon}
              <h1 className="text-2xl font-bold">{currentConfig.title}</h1>
            </div>
            <p className="text-white/90">{currentConfig.subtitle}</p>
            <p className="text-sm text-white/70 mt-1">
              {currentTime.toLocaleString('id-ID', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
          <div className="text-right">
            <div className="text-3xl font-bold">LaundryPro</div>
            <div className="text-sm text-white/70">Laundry Berkah Jaya</div>
          </div>
        </div>
      </div>

      {/* Quick Metrics */}
      <QuickMetrics />

      {/* Contextual Insights */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Smart Insights</h2>
        <div className="space-y-4">
          {insights.map((insight) => (
            <ProgressiveCard
              key={insight.id}
              insight={insight}
              isExpanded={expandedCard === insight.id}
              onToggle={(id) => setExpandedCard(expandedCard === id ? null : id)}
            />
          ))}
        </div>
      </div>

      {/* Context Mode Simulator */}
      <div className="mt-6 bg-white rounded-lg shadow-sm p-4">
        <h3 className="font-semibold text-gray-900 mb-3">Demo: Simulasi Context Mode</h3>
        <div className="flex gap-2">
          {Object.keys(contextConfig).map((mode) => (
            <button
              key={mode}
              onClick={() => setContextMode(mode)}
              className={`px-3 py-2 rounded text-sm font-medium transition-colors ${
                contextMode === mode 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {contextConfig[mode].title}
            </button>
          ))}
        </div>
        <p className="text-xs text-gray-500 mt-2">
          * Dalam implementasi nyata, context berubah otomatis berdasarkan waktu dan behavior pattern
        </p>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-10px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
        .scale-102 {
          transform: scale(1.02);
        }
      `}</style>
    </div>
  );
};

export default LaundryDashboard;