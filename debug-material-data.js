#!/usr/bin/env node

/**
 * Debug script to check material usage data
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function debugMaterialData() {
  console.log('🔍 Debugging Material Usage Data');
  console.log('=================================\n');

  try {
    // Check materials
    const materials = await prisma.material.findMany({
      select: {
        id: true,
        material_name: true,
        _count: {
          select: {
            transaction_materials: true
          }
        }
      }
    });

    console.log(`📦 Total Materials: ${materials.length}`);
    console.log('Material Usage Counts:');
    materials.forEach(material => {
      console.log(`  - ${material.material_name}: ${material._count.transaction_materials} usage records`);
    });

    // Check transaction materials
    const transactionMaterials = await prisma.transactionMaterial.findMany({
      take: 10,
      include: {
        material: {
          select: { material_name: true }
        },
        transaction: {
          select: { 
            transaction_date: true,
            context_day_type: true,
            context_time_of_day: true,
            weight_kg: true
          }
        }
      }
    });

    console.log(`\n🔗 Total Transaction Materials: ${transactionMaterials.length}`);
    console.log('Sample Transaction Materials:');
    transactionMaterials.slice(0, 5).forEach((tm, index) => {
      console.log(`  ${index + 1}. ${tm.material.material_name}: ${tm.quantity_used} units`);
      console.log(`     Date: ${tm.transaction.transaction_date}`);
      console.log(`     Context: ${tm.transaction.context_day_type}, ${tm.transaction.context_time_of_day}`);
    });

    // Check analysis period
    const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000);
    console.log(`\n📅 Analysis Period: Last 180 days (since ${sixMonthsAgo.toISOString().split('T')[0]})`);

    // Check materials with sufficient data
    const materialsWithSufficientData = await prisma.material.findMany({
      where: {
        transaction_materials: {
          some: {
            transaction: {
              transaction_date: {
                gte: sixMonthsAgo
              }
            }
          }
        }
      },
      include: {
        transaction_materials: {
          where: {
            transaction: {
              transaction_date: {
                gte: sixMonthsAgo
              }
            }
          },
          include: {
            transaction: {
              select: {
                transaction_date: true,
                context_day_type: true,
                context_time_of_day: true,
                weight_kg: true
              }
            }
          }
        }
      }
    });

    console.log(`\n✅ Materials with data in analysis period: ${materialsWithSufficientData.length}`);
    materialsWithSufficientData.forEach(material => {
      console.log(`  - ${material.material_name}: ${material.transaction_materials.length} records`);
    });

    // Check minimum threshold
    const MINIMUM_USAGE_RECORDS = 5;
    const materialsAboveThreshold = materialsWithSufficientData.filter(
      material => material.transaction_materials.length >= MINIMUM_USAGE_RECORDS
    );

    console.log(`\n🎯 Materials above minimum threshold (${MINIMUM_USAGE_RECORDS} records): ${materialsAboveThreshold.length}`);
    materialsAboveThreshold.forEach(material => {
      console.log(`  ✅ ${material.material_name}: ${material.transaction_materials.length} records`);
    });

    if (materialsAboveThreshold.length === 0) {
      console.log('\n❌ No materials have sufficient data for pattern analysis!');
      console.log('💡 Recommendation: Lower the MINIMUM_USAGE_RECORDS threshold or add more transaction data.');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

debugMaterialData();
