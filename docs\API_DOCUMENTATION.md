# LaundrySense API Documentation

## 📋 **OVERVIEW**

Dokumentasi lengkap untuk REST API endpoints yang telah diimplementasikan dalam LaundrySense Management System.

**Base URL**: `http://localhost:3000/api`  
**Content-Type**: `application/json`  
**Authentication**: Not implemented yet (planned for Phase 5)

---

## 🔗 **API ENDPOINTS**

### **Customers API**

#### **GET /api/customers**
Mengambil daftar pelanggan dengan pagination dan filtering.

**Query Parameters:**
```typescript
{
  page?: number;        // Default: 1
  limit?: number;       // Default: 10
  search?: string;      // Search by name, phone, or email
  sortBy?: string;      // Field to sort by
  sortOrder?: 'asc' | 'desc'; // Sort direction
}
```

**Response:**
```typescript
{
  success: boolean;
  data: Customer[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface Customer {
  id: string;
  name: string;
  phone_number: string;
  email?: string;
  address?: string;
  registration_date: string;
  last_transaction_date?: string;
  behavior_frequency_score: number;
  behavior_preference_score: number;
  behavior_seasonal_bias: number;
  _count?: {
    transactions: number;
  };
}
```

#### **POST /api/customers**
Membuat pelanggan baru.

**Request Body:**
```typescript
{
  name: string;                    // Required
  phone_number: string;            // Required, unique
  email?: string;                  // Optional, unique if provided
  address?: string;                // Optional
  behavior_frequency_score: number;   // 0.0 - 1.0
  behavior_preference_score: number;  // 0.0 - 1.0
  behavior_seasonal_bias: number;     // 0.0 - 1.0
}
```

**Response:**
```typescript
{
  success: boolean;
  data: Customer;
  message: string;
}
```

#### **PUT /api/customers/[id]**
Mengupdate data pelanggan.

**Request Body:** Same as POST (all fields optional)

#### **DELETE /api/customers/[id]**
Menghapus pelanggan.

**Response:**
```typescript
{
  success: boolean;
  message: string;
}
```

---

### **Materials API**

#### **GET /api/materials**
Mengambil daftar material dengan pagination dan filtering.

**Query Parameters:**
```typescript
{
  page?: number;
  limit?: number;
  search?: string;      // Search by material name
  category?: string;    // Filter by category
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```

**Response:**
```typescript
{
  success: boolean;
  data: Material[];
  pagination: PaginationInfo;
}

interface Material {
  id: string;
  material_name: string;
  current_stock_unit: number;
  unit_of_measure: string;
  last_restock_date: string;
  usage_rate_per_transaction: number;
  usage_rate_per_kg: number;
  minimum_stock_threshold: number;
  cost_per_unit: number;
  supplier_info?: string;
  category: string;
}
```

#### **POST /api/materials**
Membuat material baru.

**Request Body:**
```typescript
{
  material_name: string;              // Required
  current_stock_unit: number;         // Required, >= 0
  unit_of_measure: string;            // Required
  usage_rate_per_transaction: number; // >= 0
  usage_rate_per_kg: number;          // >= 0
  minimum_stock_threshold: number;    // >= 0
  cost_per_unit: number;              // >= 0
  supplier_info?: string;             // Optional
  category: string;                   // Required
}
```

**Categories:**
- `DETERGENT`
- `FABRIC_SOFTENER`
- `BLEACH`
- `STAIN_REMOVER`
- `PACKAGING`
- `EQUIPMENT`
- `FRAGRANCE`
- `OTHER`

#### **PUT /api/materials/[id]**
Mengupdate data material.

#### **DELETE /api/materials/[id]**
Menghapus material.

---

### **Transactions API**

#### **GET /api/transactions**
Mengambil daftar transaksi dengan pagination dan filtering.

**Query Parameters:**
```typescript
{
  page?: number;
  limit?: number;
  search?: string;      // Search by customer name
  status?: string;      // Filter by status
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}
```

**Response:**
```typescript
{
  success: boolean;
  data: Transaction[];
  pagination: PaginationInfo;
}

interface Transaction {
  id: string;
  customer_id: string;
  service_type: string;
  weight_kg: number;
  price: number;
  transaction_date: string;
  status: string;
  context_weather: string;
  context_day_type: string;
  context_seasonal_factor: number;
  context_time_of_day: string;
  pickup_date?: string;
  delivery_date?: string;
  special_instructions?: string;
  discount_applied: number;
  customer: {
    name: string;
    phone_number: string;
  };
}
```

#### **POST /api/transactions**
Membuat transaksi baru.

**Request Body:**
```typescript
{
  customer_id: string;              // Required
  service_type: string;             // Required
  weight_kg: number;                // Required, > 0
  price: number;                    // Required, >= 0
  status: string;                   // Default: 'PENDING'
  context_weather: string;          // Required
  context_day_type: string;         // Required
  context_seasonal_factor: number;  // 0.0 - 1.0
  context_time_of_day: string;      // Required
  pickup_date?: string;             // Optional, ISO date
  delivery_date?: string;           // Optional, ISO date
  special_instructions?: string;    // Optional
  discount_applied: number;         // Default: 0
  materials?: Array<{               // Optional
    material_id: string;
    quantity_used: number;
  }>;
}
```

**Service Types:**
- `CUCI_KERING` - Cuci & Kering
- `CUCI_SETRIKA` - Cuci & Setrika
- `SETRIKA_SAJA` - Setrika Saja
- `CUCI_SAJA` - Cuci Saja
- `DRY_CLEAN` - Dry Clean
- `SEPATU` - Cuci Sepatu
- `KARPET` - Cuci Karpet
- `SELIMUT` - Cuci Selimut

**Transaction Status:**
- `PENDING` - Menunggu
- `IN_PROGRESS` - Sedang Diproses
- `READY` - Siap Diambil
- `COMPLETED` - Selesai
- `CANCELLED` - Dibatalkan

#### **PUT /api/transactions/[id]**
Mengupdate data transaksi.

#### **DELETE /api/transactions/[id]**
Menghapus transaksi.

---

### **Analytics API**

#### **GET /api/analytics**
Mengambil data analytics dan laporan.

**Query Parameters:**
```typescript
{
  range?: '7d' | '30d' | '90d';    // Default: '30d'
}
```

**Response:**
```typescript
{
  revenue: {
    daily: Array<{ date: string; amount: number }>;
    monthly: Array<{ month: string; amount: number }>;
    growth: number;  // Percentage growth
  };
  transactions: {
    daily: Array<{ date: string; count: number }>;
    byService: Array<{
      service: string;
      count: number;
      percentage: number;
    }>;
    avgValue: number;
  };
  customers: {
    new: Array<{ date: string; count: number }>;
    returning: Array<{ date: string; count: number }>;
    retention: number;  // Percentage
  };
  materials: {
    usage: Array<{
      material: string;
      used: number;
      cost: number;
    }>;
    efficiency: number;  // Percentage
  };
}
```

---

### **Inventory API**

#### **GET /api/inventory/low-stock**
Mengambil daftar material dengan stok rendah.

**Response:**
```typescript
{
  success: boolean;
  data: Array<{
    id: string;
    name: string;
    currentStock: number;
    minStock: number;
    unit: string;
  }>;
}
```

---

## 🔧 **ERROR HANDLING**

### **Standard Error Response**
```typescript
{
  success: false;
  error: string;        // Error message
  details?: any;        // Additional error details
  code?: string;        // Error code
}
```

### **HTTP Status Codes**
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `500` - Internal Server Error

### **Common Error Scenarios**

#### **Validation Errors (400)**
```typescript
{
  success: false;
  error: "Validation failed",
  details: {
    field: "phone_number",
    message: "Phone number is required"
  }
}
```

#### **Not Found (404)**
```typescript
{
  success: false;
  error: "Customer not found",
  code: "CUSTOMER_NOT_FOUND"
}
```

#### **Duplicate Data (409)**
```typescript
{
  success: false;
  error: "Phone number already exists",
  code: "DUPLICATE_PHONE_NUMBER"
}
```

---

## 📊 **PAGINATION**

All list endpoints support pagination with consistent format:

**Request:**
```
GET /api/customers?page=2&limit=20
```

**Response:**
```typescript
{
  success: true;
  data: Customer[];
  pagination: {
    page: 2;
    limit: 20;
    total: 150;
    totalPages: 8;
  };
}
```

---

## 🔍 **FILTERING & SEARCH**

### **Search Parameters**
- **Customers**: Search by name, phone_number, or email
- **Materials**: Search by material_name
- **Transactions**: Search by customer name

### **Filter Parameters**
- **Materials**: Filter by category
- **Transactions**: Filter by status
- **Analytics**: Filter by date range

### **Sorting**
All list endpoints support sorting:
```
GET /api/customers?sortBy=name&sortOrder=asc
```

---

## 🚀 **USAGE EXAMPLES**

### **Create Customer**
```javascript
const response = await fetch('/api/customers', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    name: 'John Doe',
    phone_number: '+6281234567890',
    email: '<EMAIL>',
    behavior_frequency_score: 0.7,
    behavior_preference_score: 0.8,
    behavior_seasonal_bias: 0.5
  })
});

const result = await response.json();
```

### **Get Transactions with Filter**
```javascript
const response = await fetch('/api/transactions?status=PENDING&page=1&limit=10');
const result = await response.json();
```

### **Get Analytics Data**
```javascript
const response = await fetch('/api/analytics?range=30d');
const analytics = await response.json();
```

---

## 📋 **RATE LIMITING**

**Current Status**: Not implemented  
**Planned**: Phase 5 (Production Hardening)

**Planned Limits:**
- 100 requests per 15 minutes per IP
- 1000 requests per hour per authenticated user
- Burst limit: 20 requests per minute

---

## 🔐 **AUTHENTICATION**

**Current Status**: Not implemented  
**Planned**: Phase 5 (Production Hardening)

**Planned Implementation:**
- JWT-based authentication
- Role-based access control
- API key authentication for external integrations

---

**Document Version**: 1.0  
**Last Updated**: Juny 2025
