# LaundrySense - Phase 1B: Pattern Detection Engine

## Overview
Phase 1B implements the Pattern Detection Engine for LaundrySense, providing statistical analysis and intelligence capabilities to identify customer behavior patterns, material usage trends, and revenue patterns.

## 🧠 Pattern Analysis Architecture

### Core Components
1. **Customer Pattern Analyzer** - Behavioral intelligence and loyalty scoring
2. **Material Usage Pattern Analyzer** - Consumption trends and stock optimization
3. **Revenue Pattern Analyzer** - Financial trends and peak detection
4. **Pattern Calculation Service** - Orchestrates all analysis engines
5. **Background Job System** - Automated pattern calculation scheduling

## 📊 Pattern Analysis Algorithms

### Customer Behavior Patterns

#### Frequency Analysis
- **Calculated Frequency**: Transactions per month based on historical data
- **Frequency Trend**: Increasing/decreasing/stable pattern detection
- **Recency Analysis**: Days since last transaction

#### Preference Analysis
- **Service Type Preference**: Most frequently used services with confidence scoring
- **Average Spending**: Customer value analysis
- **Weight Patterns**: Typical laundry volume per transaction

#### Seasonal Analysis
- **Monthly Activity Patterns**: JSON-stored seasonal behavior
- **Peak/Low Months**: Identification of high and low activity periods
- **Seasonal Variance**: Measure of seasonal variation (coefficient of variation)

#### Behavioral Scoring (0.0-1.0)
- **Loyalty Score**: Consistency and lifetime value
- **Value Score**: Spending patterns relative to average
- **Predictability Score**: Regularity of transaction intervals

### Material Usage Patterns

#### Consumption Analysis
- **Average Consumption Rate**: Units consumed per day
- **Consumption Trend**: Increasing/decreasing/stable usage patterns
- **Peak Usage Patterns**: Day type and time of day analysis

#### Seasonal Analysis
- **Monthly Consumption Patterns**: Seasonal adjustment factors
- **Peak/Low Consumption Months**: Seasonal demand identification
- **Seasonal Multiplier**: Peak vs average consumption ratio

#### Efficiency Metrics
- **Cost Efficiency Score**: Cost per transaction optimization
- **Usage Efficiency Score**: Usage per kg optimization
- **Waste Indicator**: Variance in usage patterns (potential waste detection)

#### Stock Predictions
- **Predicted Days to Empty**: Stock depletion forecasting
- **Reorder Recommendations**: Automated reorder alerts
- **Optimal Stock Level**: Recommended inventory levels

### Revenue Patterns

#### Daily Metrics
- **Daily Revenue**: Total revenue per day
- **Transaction Count**: Number of transactions per day
- **Average Transaction Value**: Revenue per transaction
- **Weight Total**: Total laundry processed

#### Trend Analysis
- **Revenue Classification**: Peak/high/normal/low/valley classification
- **Peak Status**: Daily/weekly/monthly peak identification
- **Growth Rate**: Week-over-week growth analysis

#### Contextual Analysis
- **Day Type Impact**: Weekday vs weekend performance
- **Weather Context**: Weather impact on business
- **Seasonal Factors**: Seasonal business variations

## 🔧 Confidence Scoring System

### Customer Pattern Confidence
- **Data Quantity Factor** (40%): Number of transactions analyzed
- **Time Period Factor** (30%): Length of analysis period
- **Stability Factor** (30%): Consistency of patterns

### Material Pattern Confidence
- **Data Quantity Factor** (40%): Number of usage records
- **Time Period Factor** (30%): Analysis period coverage
- **Seasonal Completeness Factor** (30%): Months with data

### Revenue Pattern Confidence
- **Data Completeness** (70%): Transaction count vs expected
- **Recency Factor** (30%): How recent the data is

## 🗄️ Database Schema

### Pattern Storage Tables

#### CustomerPattern
```sql
- customer_id (FK, unique)
- calculated_frequency, frequency_trend
- preferred_service_type, confidence scores
- seasonal_activity_json, peak/low months
- loyalty_score, value_score, predictability_score
- confidence_score, data_points_count
```

#### MaterialUsagePattern
```sql
- material_id (FK, unique)
- average_consumption_rate, consumption_trend
- peak_usage_day_type, peak_usage_time
- seasonal_adjustment_json, seasonal_multiplier
- efficiency scores, waste_indicator
- stock predictions, reorder_recommendation
```

#### RevenueTrend
```sql
- date (unique)
- daily metrics (revenue, transactions, weight)
- weekly/monthly context and rankings
- trend classification, peak status
- growth_rate, contextual data
```

#### PatternCalculationLog
```sql
- calculation_type, status, timing
- records_processed, records_updated
- error_message, execution_time_ms
```

## 🚀 API Endpoints

### Customer Patterns
- `GET /api/patterns/customers` - List customer patterns with filtering
- `GET /api/patterns/customers/[id]` - Get specific customer pattern
- `POST /api/patterns/customers/calculate` - Trigger pattern calculation

### Material Patterns
- `GET /api/patterns/materials` - List material usage patterns
- `POST /api/patterns/materials/calculate` - Trigger material analysis

### Revenue Patterns
- `GET /api/patterns/revenue` - List revenue trends with date filtering
- `POST /api/patterns/revenue/calculate` - Trigger revenue analysis

### API Features
- **Pagination**: Efficient data retrieval
- **Filtering**: Confidence scores, date ranges, specific criteria
- **Insights Generation**: Automated insights and recommendations
- **Error Handling**: Comprehensive error responses

## ⏰ Background Job System

### Cron Job Scheduling
```typescript
// Daily calculation at 2 AM
cron.schedule('0 2 * * *', async () => {
  await patternCalculationJob.runFullCalculation();
});

// Weekly calculation on Sunday at 3 AM
cron.schedule('0 3 * * 0', async () => {
  await patternCalculationJob.runFullCalculation();
});
```

### Manual Execution
```bash
# Calculate all patterns
npm run patterns:calculate

# Calculate specific pattern types
npm run patterns:customers
npm run patterns:materials
npm run patterns:revenue
```

### Job Management
- **Status Tracking**: Running/completed/failed status
- **Error Handling**: Graceful error recovery
- **Logging**: Detailed execution logs
- **Performance Monitoring**: Execution time tracking

## 🧪 Testing Framework

### Unit Tests
- **Customer Pattern Analysis**: Behavioral scoring algorithms
- **Material Pattern Analysis**: Consumption and efficiency calculations
- **Revenue Pattern Analysis**: Trend detection and classification
- **API Endpoints**: Request/response validation

### Test Coverage
- **Algorithm Accuracy**: Pattern detection correctness
- **Edge Cases**: Insufficient data, zero values, extreme scenarios
- **Error Handling**: Graceful failure scenarios
- **Performance**: Execution time benchmarks

## 📈 Usage Examples

### Calculate Customer Patterns
```bash
# Calculate all customer patterns
curl -X POST http://localhost:3000/api/patterns/customers/calculate

# Calculate specific customer
curl -X POST http://localhost:3000/api/patterns/customers/calculate \
  -H "Content-Type: application/json" \
  -d '{"customer_id": "customer_id_here"}'
```

### Get Material Insights
```bash
# Get materials needing reorder
curl "http://localhost:3000/api/patterns/materials?reorder_only=true"

# Get high-confidence patterns
curl "http://localhost:3000/api/patterns/materials?min_confidence=0.8"
```

### Analyze Revenue Trends
```bash
# Get last 30 days revenue patterns
curl "http://localhost:3000/api/patterns/revenue?limit=30"

# Get peak revenue days
curl "http://localhost:3000/api/patterns/revenue?trend_type=peak"
```

## 🎯 Key Features

### Smart Insights
- **Automated Pattern Recognition**: Statistical analysis without ML complexity
- **Confidence Scoring**: Reliability indicators for all patterns
- **Contextual Intelligence**: Weather, seasonal, and temporal factors
- **Actionable Recommendations**: Business-focused suggestions

### Performance Optimizations
- **Efficient Algorithms**: O(n) complexity for most calculations
- **Database Indexing**: Optimized queries for pattern retrieval
- **Background Processing**: Non-blocking pattern calculations
- **Caching Strategy**: Pre-calculated patterns for fast API responses

### Business Intelligence
- **Customer Segmentation**: Loyalty and value scoring
- **Inventory Optimization**: Stock level recommendations
- **Revenue Forecasting**: Trend-based predictions
- **Operational Insights**: Peak time and seasonal analysis

## 🔄 Integration with Phase 1A

### Data Sources
- **Customer Data**: Registration and behavioral information
- **Transaction Data**: Service history with contextual metadata
- **Material Usage**: Consumption tracking per transaction
- **Inventory Data**: Current stock levels and costs

### Pattern Storage
- **Calculated Patterns**: Stored in dedicated pattern tables
- **Historical Tracking**: Pattern evolution over time
- **Confidence Metrics**: Data quality indicators
- **Calculation Logs**: Audit trail for pattern updates

## 📋 Next Steps

Phase 1B provides the foundation for:
- **Phase 2**: Contextual Intelligence Layer with real-time insights
- **Phase 3**: UI Dashboard with pattern visualization
- **Phase 4**: Advanced analytics and machine learning integration

The Pattern Detection Engine is designed to scale and evolve with more sophisticated algorithms while maintaining the current statistical foundation.
