// Optional: configure or set up a testing framework before each test.
// If you delete this file, remove `setupFilesAfterEnv` from `jest.config.js`

// Mock environment variables for testing
process.env.DATABASE_URL = process.env.DATABASE_URL || 'mysql://root@localhost:3306/laundrysense_test'

// Global test setup
beforeAll(() => {
  // Any global setup
})

afterAll(() => {
  // Any global cleanup
})
