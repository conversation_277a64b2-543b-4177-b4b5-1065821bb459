import { PrismaClient, ServiceType, TransactionStatus, WeatherContext, DayType, TimeOfDay, MaterialCategory } from '../src/generated/prisma';

const prisma = new PrismaClient();

// Helper function to generate random date within range
function randomDate(start: Date, end: Date): Date {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

// Helper function to get random enum value
function randomEnum<T>(enumObject: T): T[keyof T] {
  const values = Object.values(enumObject as any);
  return values[Math.floor(Math.random() * values.length)];
}

// Helper function to generate realistic phone numbers
function generatePhoneNumber(): string {
  const prefixes = ['081', '082', '083', '085', '087', '088', '089'];
  const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
  const suffix = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
  return `${prefix}${suffix}`;
}

async function seedMaterials() {
  console.log('🧴 Seeding materials...');
  
  const materials = [
    {
      material_name: 'Deterjen Cair Premium',
      current_stock_unit: 50.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.2,
      usage_rate_per_kg: 0.1,
      minimum_stock_threshold: 10.0,
      cost_per_unit: 15000,
      supplier_info: 'PT Unilever Indonesia',
      category: MaterialCategory.DETERGENT,
    },
    {
      material_name: 'Pewangi Pakaian Lavender',
      current_stock_unit: 30.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.05,
      usage_rate_per_kg: 0.02,
      minimum_stock_threshold: 5.0,
      cost_per_unit: 25000,
      supplier_info: 'CV Aroma Segar',
      category: MaterialCategory.FRAGRANCE,
    },
    {
      material_name: 'Pelembut Kain',
      current_stock_unit: 25.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.1,
      usage_rate_per_kg: 0.05,
      minimum_stock_threshold: 8.0,
      cost_per_unit: 18000,
      supplier_info: 'PT Kao Indonesia',
      category: MaterialCategory.FABRIC_SOFTENER,
    },
    {
      material_name: 'Pemutih Pakaian',
      current_stock_unit: 15.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.03,
      usage_rate_per_kg: 0.01,
      minimum_stock_threshold: 3.0,
      cost_per_unit: 12000,
      supplier_info: 'PT Wings Surya',
      category: MaterialCategory.BLEACH,
    },
    {
      material_name: 'Penghilang Noda',
      current_stock_unit: 20.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.02,
      usage_rate_per_kg: 0.008,
      minimum_stock_threshold: 4.0,
      cost_per_unit: 22000,
      supplier_info: 'PT Procter & Gamble',
      category: MaterialCategory.STAIN_REMOVER,
    },
    {
      material_name: 'Plastik Pembungkus',
      current_stock_unit: 500.0,
      unit_of_measure: 'lembar',
      usage_rate_per_transaction: 2.0,
      usage_rate_per_kg: 1.0,
      minimum_stock_threshold: 100.0,
      cost_per_unit: 500,
      supplier_info: 'CV Plastik Jaya',
      category: MaterialCategory.PACKAGING,
    },
    {
      material_name: 'Hanger Plastik',
      current_stock_unit: 200.0,
      unit_of_measure: 'buah',
      usage_rate_per_transaction: 5.0,
      usage_rate_per_kg: 2.0,
      minimum_stock_threshold: 50.0,
      cost_per_unit: 1000,
      supplier_info: 'UD Hanger Murah',
      category: MaterialCategory.PACKAGING,
    },
    {
      material_name: 'Deterjen Bubuk Ekonomis',
      current_stock_unit: 40.0,
      unit_of_measure: 'kg',
      usage_rate_per_transaction: 0.15,
      usage_rate_per_kg: 0.08,
      minimum_stock_threshold: 8.0,
      cost_per_unit: 8000,
      supplier_info: 'PT Sayap Mas Utama',
      category: MaterialCategory.DETERGENT,
    },
    {
      material_name: 'Pewangi Rose',
      current_stock_unit: 20.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.04,
      usage_rate_per_kg: 0.015,
      minimum_stock_threshold: 4.0,
      cost_per_unit: 28000,
      supplier_info: 'CV Aroma Segar',
      category: MaterialCategory.FRAGRANCE,
    },
    {
      material_name: 'Oli Mesin Cuci',
      current_stock_unit: 10.0,
      unit_of_measure: 'liter',
      usage_rate_per_transaction: 0.001,
      usage_rate_per_kg: 0.0005,
      minimum_stock_threshold: 2.0,
      cost_per_unit: 45000,
      supplier_info: 'PT Shell Indonesia',
      category: MaterialCategory.EQUIPMENT,
    },
  ];

  for (const material of materials) {
    await prisma.materialInventory.upsert({
      where: { material_name: material.material_name },
      update: {},
      create: material,
    });
  }

  console.log(`✅ Created ${materials.length} materials`);
}

async function seedCustomers() {
  console.log('👥 Seeding customers...');
  
  const customerNames = [
    'Budi Santoso', 'Siti Nurhaliza', 'Ahmad Wijaya', 'Dewi Sartika', 'Rudi Hermawan',
    'Maya Sari', 'Eko Prasetyo', 'Rina Kusuma', 'Agus Setiawan', 'Lina Marlina',
    'Doni Pratama', 'Sri Wahyuni', 'Bambang Sutrisno', 'Indira Putri', 'Hendra Gunawan',
    'Ratna Sari', 'Joko Widodo', 'Fitri Handayani', 'Surya Dharma', 'Wulan Dari',
    'Andi Saputra', 'Mega Wati', 'Rizki Ramadhan', 'Sari Dewi', 'Fajar Nugroho',
    'Tina Kartika', 'Yudi Setiawan', 'Nita Anggraini', 'Dimas Pratama', 'Lia Permata'
  ];

  const addresses = [
    'Jl. Merdeka No. 123, Jakarta Pusat',
    'Jl. Sudirman No. 456, Jakarta Selatan',
    'Jl. Thamrin No. 789, Jakarta Pusat',
    'Jl. Gatot Subroto No. 321, Jakarta Selatan',
    'Jl. Kuningan No. 654, Jakarta Selatan',
    'Jl. Kemang No. 987, Jakarta Selatan',
    'Jl. Menteng No. 147, Jakarta Pusat',
    'Jl. Senayan No. 258, Jakarta Pusat',
    'Jl. Pondok Indah No. 369, Jakarta Selatan',
    'Jl. Kelapa Gading No. 741, Jakarta Utara'
  ];

  for (let i = 0; i < customerNames.length; i++) {
    const registrationDate = randomDate(new Date('2023-01-01'), new Date('2024-11-01'));
    
    await prisma.customer.create({
      data: {
        name: customerNames[i],
        phone_number: generatePhoneNumber(),
        email: `${customerNames[i].toLowerCase().replace(/\s+/g, '.')}@email.com`,
        address: addresses[i % addresses.length],
        registration_date: registrationDate,
        behavior_frequency_score: Math.random(),
        behavior_preference_score: Math.random(),
        behavior_seasonal_bias: Math.random(),
      },
    });
  }

  console.log(`✅ Created ${customerNames.length} customers`);
}

async function seedTransactions() {
  console.log('💰 Seeding transactions...');
  
  const customers = await prisma.customer.findMany();
  const materials = await prisma.materialInventory.findMany();
  
  const serviceTypePrices = {
    [ServiceType.CUCI_KERING]: { base: 8000, perKg: 6000 },
    [ServiceType.CUCI_SETRIKA]: { base: 10000, perKg: 8000 },
    [ServiceType.SETRIKA_SAJA]: { base: 5000, perKg: 4000 },
    [ServiceType.CUCI_SAJA]: { base: 6000, perKg: 4500 },
    [ServiceType.DRY_CLEAN]: { base: 15000, perKg: 12000 },
    [ServiceType.SEPATU]: { base: 20000, perKg: 0 },
    [ServiceType.KARPET]: { base: 25000, perKg: 15000 },
    [ServiceType.SELIMUT]: { base: 12000, perKg: 8000 },
  };

  // Generate transactions for the last 6 months
  const startDate = new Date('2024-05-01');
  const endDate = new Date('2024-11-30');
  
  for (let i = 0; i < 150; i++) {
    const customer = customers[Math.floor(Math.random() * customers.length)];
    const serviceType = randomEnum(ServiceType);
    const weight = Math.random() * 8 + 1; // 1-9 kg
    const transactionDate = randomDate(startDate, endDate);
    
    // Calculate price based on service type and weight
    const pricing = serviceTypePrices[serviceType];
    const basePrice = pricing.base + (pricing.perKg * weight);
    const discount = Math.random() < 0.2 ? Math.random() * 20 : 0; // 20% chance of discount
    const finalPrice = basePrice * (1 - discount / 100);

    // Determine context based on date
    const hour = Math.floor(Math.random() * 14) + 7; // 7 AM to 9 PM
    const dayOfWeek = transactionDate.getDay();
    const month = transactionDate.getMonth();
    
    const transaction = await prisma.transaction.create({
      data: {
        customer_id: customer.id,
        service_type: serviceType,
        weight_kg: Math.round(weight * 10) / 10,
        price: Math.round(finalPrice),
        transaction_date: transactionDate,
        status: randomEnum(TransactionStatus),
        context_weather: randomEnum(WeatherContext),
        context_day_type: dayOfWeek === 0 || dayOfWeek === 6 ? DayType.WEEKEND : DayType.WEEKDAY,
        context_seasonal_factor: month >= 5 && month <= 7 ? 0.8 : 0.4, // Higher in dry season
        context_time_of_day: hour < 9 ? TimeOfDay.EARLY_MORNING :
                           hour < 12 ? TimeOfDay.MORNING :
                           hour < 17 ? TimeOfDay.AFTERNOON :
                           hour < 20 ? TimeOfDay.EVENING : TimeOfDay.NIGHT,
        pickup_date: Math.random() < 0.7 ? randomDate(transactionDate, new Date(transactionDate.getTime() + 3 * 24 * 60 * 60 * 1000)) : null,
        delivery_date: Math.random() < 0.5 ? randomDate(transactionDate, new Date(transactionDate.getTime() + 5 * 24 * 60 * 60 * 1000)) : null,
        special_instructions: Math.random() < 0.3 ? 'Pisahkan pakaian putih dan berwarna' : null,
        discount_applied: discount,
      },
    });

    // Add random materials to transaction
    const numMaterials = Math.floor(Math.random() * 4) + 2; // 2-5 materials
    const usedMaterials = new Set();
    
    for (let j = 0; j < numMaterials; j++) {
      const material = materials[Math.floor(Math.random() * materials.length)];
      
      if (!usedMaterials.has(material.id)) {
        usedMaterials.add(material.id);
        
        const quantityUsed = material.usage_rate_per_kg * weight * (0.8 + Math.random() * 0.4);
        
        await prisma.transactionMaterial.create({
          data: {
            transaction_id: transaction.id,
            material_id: material.id,
            quantity_used: Math.round(quantityUsed * 1000) / 1000,
            cost_at_time: material.cost_per_unit * quantityUsed,
          },
        });
      }
    }
  }

  console.log('✅ Created 150 transactions with materials');
}

async function main() {
  console.log('🌱 Starting database seeding...');
  
  try {
    await seedMaterials();
    await seedCustomers();
    await seedTransactions();
    
    console.log('🎉 Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
