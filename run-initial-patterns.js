#!/usr/bin/env node

/**
 * LaundrySense Initial Pattern Calculation
 * 
 * This script runs pattern calculation on existing seed data
 * to generate insights for the dashboard.
 */

const { PatternCalculationService } = require('./src/lib/pattern-analysis/pattern-calculator');

async function runInitialPatterns() {
  console.log('🧠 LaundrySense Initial Pattern Calculation');
  console.log('===========================================');
  console.log('📅 Started at:', new Date().toISOString());
  console.log('🎯 Processing seed data for insights...\n');

  try {
    const calculationService = new PatternCalculationService();
    
    console.log('🔄 Calculating all patterns from seed data...');
    const results = await calculationService.calculateAllPatterns();
    
    console.log('\n📊 Pattern Calculation Results:');
    console.log('================================');
    
    let totalProcessed = 0;
    let totalUpdated = 0;
    let totalTime = 0;
    let successCount = 0;
    let failureCount = 0;

    results.forEach(result => {
      const status = result.status === 'completed' ? '✅' : '❌';
      const type = result.calculation_type.toUpperCase().replace('_', ' ');
      
      console.log(`${status} ${type}: ${result.records_updated} records updated in ${result.execution_time_ms}ms`);
      
      if (result.error_message) {
        console.log(`   Error: ${result.error_message}`);
      }
      
      totalProcessed += result.records_processed;
      totalUpdated += result.records_updated;
      totalTime += result.execution_time_ms;
      
      if (result.status === 'completed') {
        successCount++;
      } else {
        failureCount++;
      }
    });

    console.log('\n📈 Summary:');
    console.log(`   Records Processed: ${totalProcessed}`);
    console.log(`   Records Updated: ${totalUpdated}`);
    console.log(`   Total Time: ${totalTime}ms`);
    console.log(`   Successful: ${successCount}`);
    console.log(`   Failed: ${failureCount}`);
    
    if (successCount > 0) {
      console.log('\n🎉 Pattern calculation completed successfully!');
      console.log('💡 Dashboard insights should now be available.');
      console.log('🔄 Refresh your browser to see the insights.');
    } else {
      console.log('\n❌ Pattern calculation failed. Check the errors above.');
    }

  } catch (error) {
    console.error('\n❌ Fatal error during pattern calculation:', error);
    process.exit(1);
  }
}

// Run the script
runInitialPatterns()
  .then(() => {
    console.log('\n✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Script failed:', error);
    process.exit(1);
  });
