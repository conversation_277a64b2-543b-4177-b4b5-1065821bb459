#!/usr/bin/env tsx

/**
 * LaundrySense Pattern Calculation Script
 * 
 * This script can be run manually or scheduled as a cron job to calculate
 * customer patterns, material usage patterns, and revenue trends.
 * 
 * Usage:
 *   npm run patterns:calculate              # Calculate all patterns
 *   npm run patterns:calculate customers    # Calculate only customer patterns
 *   npm run patterns:calculate materials    # Calculate only material patterns
 *   npm run patterns:calculate revenue      # Calculate only revenue trends
 * 
 * Cron job example (daily at 2 AM):
 *   0 2 * * * cd /path/to/laundrysense && npm run patterns:calculate
 */

import { PatternCalculationService } from '../src/lib/pattern-analysis/pattern-calculator';

async function main() {
  const args = process.argv.slice(2);
  const calculationType = args[0] || 'all';

  console.log('🧠 LaundrySense Pattern Calculation Engine');
  console.log('==========================================');
  console.log(`📅 Started at: ${new Date().toISOString()}`);
  console.log(`🎯 Calculation type: ${calculationType}`);
  console.log('');

  const calculationService = new PatternCalculationService();

  try {
    switch (calculationType.toLowerCase()) {
      case 'customers':
      case 'customer':
        console.log('👥 Calculating customer patterns...');
        const customerResult = await calculationService.calculateCustomerPatterns();
        printResult(customerResult);
        break;

      case 'materials':
      case 'material':
        console.log('🧴 Calculating material usage patterns...');
        const materialResult = await calculationService.calculateMaterialPatterns();
        printResult(materialResult);
        break;

      case 'revenue':
      case 'revenues':
        console.log('💰 Calculating revenue trends...');
        const revenueResult = await calculationService.calculateRevenueTrends();
        printResult(revenueResult);
        break;

      case 'all':
      default:
        console.log('🔄 Calculating all patterns...');
        const allResults = await calculationService.calculateAllPatterns();
        
        console.log('\n📊 Summary of all calculations:');
        console.log('================================');
        
        let totalProcessed = 0;
        let totalUpdated = 0;
        let totalTime = 0;
        let successCount = 0;
        let failureCount = 0;

        allResults.forEach(result => {
          printResult(result);
          totalProcessed += result.records_processed;
          totalUpdated += result.records_updated;
          totalTime += result.execution_time_ms;
          
          if (result.status === 'completed') {
            successCount++;
          } else {
            failureCount++;
          }
        });

        console.log('\n🎯 Overall Summary:');
        console.log(`   ✅ Successful calculations: ${successCount}`);
        console.log(`   ❌ Failed calculations: ${failureCount}`);
        console.log(`   📊 Total records processed: ${totalProcessed}`);
        console.log(`   💾 Total records updated: ${totalUpdated}`);
        console.log(`   ⏱️  Total execution time: ${totalTime}ms (${(totalTime / 1000).toFixed(2)}s)`);
        break;
    }

    console.log('\n✅ Pattern calculation completed successfully!');
    process.exit(0);

  } catch (error) {
    console.error('\n❌ Pattern calculation failed:');
    console.error(error);
    process.exit(1);
  }
}

function printResult(result: any) {
  const status = result.status === 'completed' ? '✅' : '❌';
  const duration = (result.execution_time_ms / 1000).toFixed(2);
  
  console.log(`\n${status} ${result.calculation_type.toUpperCase()}`);
  console.log(`   📊 Records processed: ${result.records_processed}`);
  console.log(`   💾 Records updated: ${result.records_updated}`);
  console.log(`   ⏱️  Execution time: ${result.execution_time_ms}ms (${duration}s)`);
  console.log(`   📅 Started: ${result.started_at.toISOString()}`);
  console.log(`   🏁 Completed: ${result.completed_at?.toISOString() || 'N/A'}`);
  
  if (result.error_message) {
    console.log(`   ❌ Error: ${result.error_message}`);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run the script
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unhandled error:', error);
    process.exit(1);
  });
}
