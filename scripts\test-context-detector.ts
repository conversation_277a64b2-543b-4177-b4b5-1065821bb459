#!/usr/bin/env tsx

/**
 * Context Detector Test Script
 * 
 * Simple validation script to test the Context Detection System
 */

import { ContextDetector } from '../src/lib/contextual-intelligence/context-detector';
import { ContextDetectionInput } from '../src/lib/contextual-intelligence/types';

async function testContextDetector() {
  console.log('🧠 Testing LaundrySense Context Detection System');
  console.log('================================================');

  const detector = new ContextDetector();

  // Test data
  const testInput: ContextDetectionInput = {
    currentTimestamp: new Date('2024-01-15T10:30:00Z'), // Monday 10:30 AM
    recentUserActivityLog: [
      {
        timestamp: '2024-01-15T10:25:00Z',
        action: 'view_dashboard',
        user_id: 'user1'
      },
      {
        timestamp: '2024-01-15T10:20:00Z',
        action: 'view_transactions',
        user_id: 'user1'
      }
    ],
    historicalBusinessData: {
      monthly_transactions: [
        { month: '2024-01', count: 150, revenue: 7500000 },
        { month: '2023-12', count: 120, revenue: 6000000 }
      ],
      material_usage_averages: [
        {
          material_id: 'mat1',
          material_name: 'Detergent',
          average_monthly_usage: 50,
          category: 'DETERGENT'
        }
      ],
      customer_metrics: {
        total_customers: 100,
        active_customers_last_30_days: 80,
        average_transaction_value: 50000,
        customer_retention_rate: 0.85
      },
      revenue_trends: {
        daily_average: 250000,
        weekly_average: 1750000,
        monthly_average: 7500000,
        growth_rate_percentage: 15.5
      }
    },
    dataCompletenessMetrics: {
      transactions: {
        last_updated: '2024-01-15T09:00:00Z',
        completeness_percentage: 95,
        total_records: 1500,
        missing_fields_count: 5
      },
      customers: {
        last_updated: '2024-01-15T08:00:00Z',
        completeness_percentage: 98,
        total_records: 100,
        missing_fields_count: 2
      },
      materials: {
        last_updated: '2024-01-15T07:00:00Z',
        completeness_percentage: 90,
        total_records: 20,
        missing_fields_count: 2
      },
      patterns: {
        last_calculated: '2024-01-15T02:00:00Z',
        customer_patterns_count: 80,
        material_patterns_count: 18,
        revenue_trends_count: 30,
        average_confidence_score: 0.85
      }
    },
    patternData: {
      customer_patterns: {
        total_analyzed: 80,
        high_confidence_count: 65,
        average_loyalty_score: 0.75,
        average_frequency_score: 0.68,
        seasonal_variance_average: 0.3
      },
      material_patterns: {
        total_analyzed: 18,
        reorder_recommendations_count: 3,
        average_efficiency_score: 0.82,
        stock_alerts_count: 2
      },
      revenue_patterns: {
        peak_days_count: 8,
        growth_trend: 'increasing',
        seasonal_factor_average: 0.6,
        revenue_volatility: 0.15
      }
    }
  };

  try {
    console.log('\n🔍 Running context detection...');
    const result = detector.detectContext(testInput);

    console.log('\n📊 Context Detection Results:');
    console.log('==============================');
    
    console.log('\n🕐 Current Context:');
    console.log(`   Time: ${result.currentContextObject.time}`);
    console.log(`   Business Cycle: ${result.currentContextObject.businessCycle}`);
    console.log(`   User Behavior: ${result.currentContextObject.userBehavior}`);
    console.log(`   Data Quality: ${result.currentContextObject.dataQuality}`);

    console.log('\n🎯 Recommendations:');
    console.log(`   Dashboard Mode: ${result.recommendedDashboardMode}`);
    console.log(`   Priority Insights: ${result.priorityInsightsList.join(', ')}`);

    console.log('\n📈 Quality Metrics:');
    console.log(`   Data Quality Score: ${result.dataQualityScore}/100`);
    console.log(`   Context Confidence: ${(result.contextConfidence * 100).toFixed(1)}%`);

    console.log('\n💡 Context Reasoning:');
    console.log(`   Time: ${result.contextReasons.time}`);
    console.log(`   Business Cycle: ${result.contextReasons.businessCycle}`);
    console.log(`   User Behavior: ${result.contextReasons.userBehavior}`);
    console.log(`   Data Quality: ${result.contextReasons.dataQuality}`);

    console.log('\n✅ Context detection completed successfully!');

    // Test different scenarios
    console.log('\n🧪 Testing Different Scenarios:');
    console.log('================================');

    // Test evening context
    const eveningInput = {
      ...testInput,
      currentTimestamp: new Date('2024-01-15T19:00:00Z')
    };
    const eveningResult = detector.detectContext(eveningInput);
    console.log(`\n🌆 Evening Context: ${eveningResult.currentContextObject.time}`);
    console.log(`   Dashboard Mode: ${eveningResult.recommendedDashboardMode}`);

    // Test peak season
    const peakSeasonInput = {
      ...testInput,
      patternData: {
        ...testInput.patternData,
        revenue_patterns: {
          ...testInput.patternData.revenue_patterns,
          seasonal_factor_average: 0.8
        }
      }
    };
    const peakResult = detector.detectContext(peakSeasonInput);
    console.log(`\n📈 Peak Season Context: ${peakResult.currentContextObject.businessCycle}`);
    console.log(`   Priority Insights: ${peakResult.priorityInsightsList.slice(0, 3).join(', ')}`);

    // Test stress mode
    const stressInput = {
      ...testInput,
      recentUserActivityLog: Array.from({ length: 25 }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 60000).toISOString(),
        action: `action_${i}`,
        user_id: 'user1'
      }))
    };
    const stressResult = detector.detectContext(stressInput);
    console.log(`\n⚠️  Stress Mode Context: ${stressResult.currentContextObject.userBehavior}`);
    console.log(`   Dashboard Mode: ${stressResult.recommendedDashboardMode}`);

    console.log('\n🎉 All context detection scenarios tested successfully!');
    
    return true;

  } catch (error) {
    console.error('\n❌ Context detection test failed:');
    console.error(error);
    return false;
  }
}

// Run the test
if (require.main === module) {
  testContextDetector()
    .then(success => {
      if (success) {
        console.log('\n✅ Context Detector validation completed successfully!');
        process.exit(0);
      } else {
        console.log('\n❌ Context Detector validation failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Unhandled error:', error);
      process.exit(1);
    });
}
