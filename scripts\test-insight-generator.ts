#!/usr/bin/env tsx

/**
 * Smart Insight Generator Test Script
 * 
 * Comprehensive validation script for the Insight Generation Engine
 */

import { SmartInsightGenerator } from '../src/lib/contextual-intelligence/engines/smart-insight-generator';
import { ProgressiveDisclosureEngine } from '../src/lib/contextual-intelligence/engines/progressive-disclosure';
import { InsightGenerationInput } from '../src/lib/contextual-intelligence/types/insight-types';

async function testInsightGenerator() {
  console.log('🧠 Testing LaundrySense Smart Insight Generator');
  console.log('==============================================');

  const generator = new SmartInsightGenerator();
  const disclosureEngine = new ProgressiveDisclosureEngine();

  // Test scenarios
  const scenarios = [
    {
      name: 'Morning Normal Operations',
      input: createMorningScenario()
    },
    {
      name: 'Midday Peak Hours',
      input: createMiddayScenario()
    },
    {
      name: 'Evening Analysis',
      input: createEveningScenario()
    },
    {
      name: 'Stress Mode - Low Stock',
      input: createStressModeScenario()
    },
    {
      name: 'Planning Mode - Growth Analysis',
      input: createPlanningScenario()
    }
  ];

  try {
    for (const scenario of scenarios) {
      console.log(`\n📊 Testing Scenario: ${scenario.name}`);
      console.log('=' + '='.repeat(scenario.name.length + 18));

      const result = generator.generateInsights(scenario.input);

      console.log(`\n📈 Generation Results:`);
      console.log(`   Total Insights: ${result.totalGenerated}`);
      console.log(`   High Priority: ${result.highPriorityCount}`);
      console.log(`   Medium Priority: ${result.mediumPriorityCount}`);
      console.log(`   Low Priority: ${result.lowPriorityCount}`);
      console.log(`   Primary Context: ${result.contextSummary.primaryContext}`);

      console.log(`\n💡 Generated Insights:`);
      result.insights.forEach((insight, index) => {
        console.log(`\n   ${index + 1}. [${insight.priority.toUpperCase()}] ${insight.title}`);
        console.log(`      Category: ${insight.category}`);
        console.log(`      Action: ${insight.action.substring(0, 80)}...`);
        console.log(`      Time: ${insight.timeEstimate} | Confidence: ${Math.round(insight.confidence * 100)}%`);
        
        // Test progressive disclosure
        const progressiveInsight = disclosureEngine.createProgressiveInsight(insight);
        console.log(`      Progressive Layers: ${progressiveInsight.layers.length}`);
        
        // Show headline
        const headline = progressiveInsight.layers[0].content;
        console.log(`      Headline: ${headline}`);
      });

      // Test progressive disclosure interaction
      if (result.insights.length > 0) {
        console.log(`\n🔍 Testing Progressive Disclosure:`);
        let progressiveInsight = disclosureEngine.createProgressiveInsight(result.insights[0]);
        
        console.log(`   Initial Level: ${progressiveInsight.currentLevel}`);
        console.log(`   Visible Layers: ${disclosureEngine.getVisibleContent(progressiveInsight).length}`);
        
        // Simulate user interactions
        progressiveInsight = disclosureEngine.recordInteraction(progressiveInsight, 'view');
        progressiveInsight = disclosureEngine.revealNextLayer(progressiveInsight);
        progressiveInsight = disclosureEngine.recordInteraction(progressiveInsight, 'expand');
        
        console.log(`   After Interaction - Level: ${progressiveInsight.currentLevel}`);
        console.log(`   Visible Layers: ${disclosureEngine.getVisibleContent(progressiveInsight).length}`);
        console.log(`   Has More Content: ${disclosureEngine.hasMoreContent(progressiveInsight)}`);
        console.log(`   Engagement Score: ${Math.round(disclosureEngine.getEngagementScore(progressiveInsight) * 100)}%`);
      }
    }

    // Test analytics summary
    console.log(`\n📊 Testing Analytics Summary:`);
    console.log('============================');
    
    const allProgressiveInsights = scenarios.flatMap(scenario => {
      const result = generator.generateInsights(scenario.input);
      return result.insights.map(insight => {
        let progressive = disclosureEngine.createProgressiveInsight(insight);
        // Simulate some interactions
        progressive = disclosureEngine.recordInteraction(progressive, 'view');
        if (Math.random() > 0.5) {
          progressive = disclosureEngine.revealNextLayer(progressive);
          progressive = disclosureEngine.recordInteraction(progressive, 'expand');
        }
        if (Math.random() > 0.7) {
          progressive = disclosureEngine.recordInteraction(progressive, 'action');
        }
        return progressive;
      });
    });

    const analyticsSummary = disclosureEngine.generateInteractionSummary(allProgressiveInsights);
    
    console.log(`   Total Insights Analyzed: ${analyticsSummary.totalInsights}`);
    console.log(`   Average Engagement: ${Math.round(analyticsSummary.averageEngagement * 100)}%`);
    console.log(`   Completion Rate: ${Math.round(analyticsSummary.completionRate * 100)}%`);
    console.log(`   Most Engaged Categories: ${analyticsSummary.mostEngagedCategories.join(', ')}`);

    console.log('\n✅ All insight generation scenarios tested successfully!');
    return true;

  } catch (error) {
    console.error('\n❌ Insight generation test failed:');
    console.error(error);
    return false;
  }
}

function createMorningScenario(): InsightGenerationInput {
  return {
    contextObject: {
      time: 'morning',
      businessCycle: 'normal_season',
      userBehavior: 'normal_mode',
      dataQuality: 'high_confidence'
    },
    patternData: {
      customer_patterns: {
        total_analyzed: 80,
        high_confidence_count: 65,
        average_loyalty_score: 0.75,
        average_frequency_score: 0.68,
        seasonal_variance_average: 0.3
      },
      material_patterns: {
        total_analyzed: 18,
        reorder_recommendations_count: 3,
        average_efficiency_score: 0.82,
        stock_alerts_count: 2
      },
      revenue_patterns: {
        peak_days_count: 8,
        growth_trend: 'increasing',
        seasonal_factor_average: 0.6,
        revenue_volatility: 0.15
      }
    },
    realTimeData: {
      transactions: {
        today_count: 5,
        today_revenue: 250000,
        current_hour_count: 2,
        average_transaction_value: 50000,
        peak_hour_today: null
      },
      materials: [
        {
          material_id: 'det001',
          material_name: 'Deterjen Premium',
          current_stock: 25,
          minimum_threshold: 20,
          category: 'DETERGENT',
          last_restock_date: '2024-01-10',
          usage_rate_per_day: 10,
          days_until_empty: 2.5
        }
      ],
      customers: {
        new_customers_today: 2,
        returning_customers_today: 3,
        pending_orders: 1,
        overdue_pickups: 0
      },
      operations: {
        active_machines: 6,
        total_machines: 10,
        queue_length: 2,
        estimated_completion_times: [30, 45],
        staff_on_duty: 2
      }
    },
    timestamp: new Date('2024-01-15T09:30:00Z')
  };
}

function createMiddayScenario(): InsightGenerationInput {
  const base = createMorningScenario();
  return {
    ...base,
    contextObject: {
      ...base.contextObject,
      time: 'midday'
    },
    realTimeData: {
      ...base.realTimeData,
      transactions: {
        today_count: 25,
        today_revenue: 1250000,
        current_hour_count: 8,
        average_transaction_value: 50000,
        peak_hour_today: 13
      },
      operations: {
        ...base.realTimeData.operations,
        queue_length: 8,
        active_machines: 10,
        estimated_completion_times: [15, 30, 45, 60, 75, 90, 105, 120]
      }
    }
  };
}

function createEveningScenario(): InsightGenerationInput {
  const base = createMorningScenario();
  return {
    ...base,
    contextObject: {
      ...base.contextObject,
      time: 'evening'
    },
    realTimeData: {
      ...base.realTimeData,
      transactions: {
        today_count: 45,
        today_revenue: 2250000,
        current_hour_count: 3,
        average_transaction_value: 50000,
        peak_hour_today: 13
      },
      customers: {
        new_customers_today: 8,
        returning_customers_today: 37,
        pending_orders: 2,
        overdue_pickups: 1
      }
    }
  };
}

function createStressModeScenario(): InsightGenerationInput {
  const base = createMorningScenario();
  return {
    ...base,
    contextObject: {
      ...base.contextObject,
      userBehavior: 'stress_mode',
      dataQuality: 'low_confidence'
    },
    realTimeData: {
      ...base.realTimeData,
      materials: [
        {
          material_id: 'det001',
          material_name: 'Deterjen Premium',
          current_stock: 8,
          minimum_threshold: 20,
          category: 'DETERGENT',
          last_restock_date: '2024-01-10',
          usage_rate_per_day: 15,
          days_until_empty: 0.5
        }
      ]
    }
  };
}

function createPlanningScenario(): InsightGenerationInput {
  const base = createMorningScenario();
  return {
    ...base,
    contextObject: {
      ...base.contextObject,
      time: 'planning',
      userBehavior: 'growth_mode'
    },
    patternData: {
      ...base.patternData,
      revenue_patterns: {
        ...base.patternData.revenue_patterns,
        growth_trend: 'increasing',
        seasonal_factor_average: 0.8
      }
    }
  };
}

// Run the test
if (require.main === module) {
  testInsightGenerator()
    .then(success => {
      if (success) {
        console.log('\n✅ Smart Insight Generator validation completed successfully!');
        process.exit(0);
      } else {
        console.log('\n❌ Smart Insight Generator validation failed!');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('❌ Unhandled error:', error);
      process.exit(1);
    });
}
