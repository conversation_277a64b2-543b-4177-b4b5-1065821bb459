import { createServer, IncomingMessage, ServerResponse } from 'http';
import { parse } from 'url';
import next from 'next';
import { WebSocketServer, WebSocket } from 'ws';
import { PrismaClient } from '@prisma/client';
import { getRealTimeKpis } from '@/lib/data-access/kpis';
import { getPatternData } from '@/lib/data-access/patterns';

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3000;

// Initialize Next.js app and Prisma Client
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();
const prisma = new PrismaClient();

app.prepare().then(() => {
  // Create a single HTTP server to handle both Next.js and WebSocket traffic
  const httpServer = createServer((req: IncomingMessage, res: ServerResponse) => {
    try {
      const parsedUrl = parse(req.url!, true);
      handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error handling request:', err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Mount the WebSocket server on the HTTP server
  const wss = new WebSocketServer({ server: httpServer });

  // Helper function to broadcast data to all connected clients
  const broadcast = (data: object) => {
    const jsonData = JSON.stringify(data);
    wss.clients.forEach((client: WebSocket) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(jsonData);
      }
    });
  };

  // Central function to fetch the latest dashboard state from the database
  const fetchLatestDashboardState = async () => {
    try {
      const [kpis, patterns] = await Promise.all([
        getRealTimeKpis(prisma),
        getPatternData(prisma),
      ]);
      return { kpis, patterns };
    } catch (error) {
      console.error('[WSS] Error fetching latest dashboard state:', error);
      return { kpis: null, patterns: null }; // Return a safe state on error
    }
  };

  // Main WebSocket connection handler
  wss.on('connection', async (ws: WebSocket) => {
    console.log('✅ WebSocket client connected');

    // 1. Send initial state to the newly connected client
    console.log('[WSS] Fetching initial state for new client...');
    const initialState = await fetchLatestDashboardState();
    ws.send(JSON.stringify({ 
      type: 'INITIAL_STATE', 
      payload: initialState 
    }));

    // 2. Listen for messages from this specific client
    ws.on('message', async (message: Buffer) => {
      try {
        const parsedMessage = JSON.parse(message.toString());
        console.log('[WSS] Received message:', parsedMessage);

        // If a trigger is received, fetch the latest state and broadcast to ALL clients
        if (parsedMessage.type === 'TRIGGER_ANALYSIS') {
          console.log('[WSS] Analysis trigger received. Fetching and broadcasting latest state...');
          const latestState = await fetchLatestDashboardState();
          broadcast({
            type: 'DASHBOARD_STATE_UPDATE',
            payload: latestState,
          });
        }
      } catch (error) {
        console.error('[WSS] Error processing message:', error);
      }
    });

    // 3. Handle client disconnection
    ws.on('close', () => {
      console.log('❌ WebSocket client disconnected');
    });

    // 4. Handle errors
    ws.on('error', (error: Error) => {
      console.error('WebSocket error:', error);
    });
  });

  // Start the HTTP server
  httpServer
    .listen(port, () => {
      console.log(`> Next.js server ready on http://${hostname}:${port}`);
      console.log(`> WebSocket server listening on ws://${hostname}:${port}`);
    })
    .on('error', (err: Error) => {
      console.error('HTTP Server Error:', err);
      process.exit(1);
    });
});