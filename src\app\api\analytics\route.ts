import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const range = searchParams.get('range') || '30d';

    // Calculate date range
    const now = new Date();
    const daysBack = range === '7d' ? 7 : range === '30d' ? 30 : 90;
    const startDate = new Date(now.getTime() - (daysBack * 24 * 60 * 60 * 1000));

    // Revenue analytics
    const revenueData = await prisma.transaction.groupBy({
      by: ['transaction_date'],
      where: {
        transaction_date: {
          gte: startDate,
        },
        status: {
          in: ['COMPLETED', 'READY'],
        },
      },
      _sum: {
        price: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        transaction_date: 'asc',
      },
    });

    // Service type distribution
    const serviceDistribution = await prisma.transaction.groupBy({
      by: ['service_type'],
      where: {
        transaction_date: {
          gte: startDate,
        },
      },
      _count: {
        id: true,
      },
    });

    const totalTransactions = serviceDistribution.reduce((sum, item) => sum + item._count.id, 0);

    // Customer analytics
    const newCustomers = await prisma.customer.groupBy({
      by: ['registration_date'],
      where: {
        registration_date: {
          gte: startDate,
        },
      },
      _count: {
        id: true,
      },
      orderBy: {
        registration_date: 'asc',
      },
    });

    // Returning customers (customers with more than 1 transaction)
    const returningCustomersData = await prisma.customer.findMany({
      where: {
        transactions: {
          some: {
            transaction_date: {
              gte: startDate,
            },
          },
        },
      },
      include: {
        _count: {
          select: {
            transactions: {
              where: {
                transaction_date: {
                  gte: startDate,
                },
              },
            },
          },
        },
      },
    });

    const returningCustomers = returningCustomersData.filter(customer => customer._count.transactions > 1);
    const retentionRate = returningCustomersData.length > 0
      ? (returningCustomers.length / returningCustomersData.length) * 100
      : 0;

    // Material usage analytics
    const materialUsage = await prisma.transactionMaterial.groupBy({
      by: ['material_id'],
      where: {
        transaction: {
          transaction_date: {
            gte: startDate,
          },
        },
      },
      _sum: {
        quantity_used: true,
      },
    });

    const materialUsageWithDetails = await Promise.all(
      materialUsage.map(async (usage) => {
        const material = await prisma.material.findUnique({
          where: { id: usage.material_id },
          select: { material_name: true, cost_per_unit: true },
        });

        return {
          material: material?.material_name || 'Unknown',
          used: usage._sum.quantity_used || 0,
          cost: (usage._sum.quantity_used || 0) * (material?.cost_per_unit || 0),
        };
      })
    );

    // Calculate previous period for growth comparison
    const previousStartDate = new Date(startDate.getTime() - (daysBack * 24 * 60 * 60 * 1000));
    const previousRevenueData = await prisma.transaction.aggregate({
      where: {
        transaction_date: {
          gte: previousStartDate,
          lt: startDate,
        },
        status: {
          in: ['COMPLETED', 'READY'],
        },
      },
      _sum: {
        price: true,
      },
    });

    const currentRevenue = revenueData.reduce((sum, item) => sum + (item._sum.price || 0), 0);
    const previousRevenue = previousRevenueData._sum.price || 0;
    const revenueGrowth = previousRevenue > 0
      ? ((currentRevenue - previousRevenue) / previousRevenue) * 100
      : 0;

    // Calculate average transaction value
    const totalTransactionValue = revenueData.reduce((sum, item) => sum + (item._sum.price || 0), 0);
    const totalTransactionCount = revenueData.reduce((sum, item) => sum + item._count.id, 0);
    const avgTransactionValue = totalTransactionCount > 0 ? totalTransactionValue / totalTransactionCount : 0;

    // Material efficiency (placeholder calculation)
    const totalMaterialCost = materialUsageWithDetails.reduce((sum, item) => sum + item.cost, 0);
    const materialEfficiency = totalMaterialCost > 0 && currentRevenue > 0
      ? ((currentRevenue - totalMaterialCost) / currentRevenue) * 100
      : 0;

    // Format response
    const analyticsData = {
      revenue: {
        daily: revenueData.map(item => ({
          date: item.transaction_date.toISOString().split('T')[0],
          amount: item._sum.price || 0,
        })),
        monthly: [
          {
            month: now.toISOString().split('T')[0].substring(0, 7),
            amount: currentRevenue,
          },
        ],
        growth: revenueGrowth,
      },
      transactions: {
        daily: revenueData.map(item => ({
          date: item.transaction_date.toISOString().split('T')[0],
          count: item._count.id,
        })),
        byService: serviceDistribution.map(item => ({
          service: getServiceTypeLabel(item.service_type),
          count: item._count.id,
          percentage: totalTransactions > 0 ? (item._count.id / totalTransactions) * 100 : 0,
        })),
        avgValue: avgTransactionValue,
      },
      customers: {
        new: newCustomers.map(item => ({
          date: item.registration_date.toISOString().split('T')[0],
          count: item._count.id,
        })),
        returning: [], // Could be implemented with more complex grouping
        retention: retentionRate,
      },
      materials: {
        usage: materialUsageWithDetails,
        efficiency: materialEfficiency,
      },
    };

    return NextResponse.json(analyticsData);

  } catch (error) {
    console.error('Analytics API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

function getServiceTypeLabel(serviceType: string): string {
  const serviceTypes: Record<string, string> = {
    'CUCI_KERING': 'Cuci & Kering',
    'CUCI_SETRIKA': 'Cuci & Setrika',
    'SETRIKA_SAJA': 'Setrika Saja',
    'CUCI_SAJA': 'Cuci Saja',
    'DRY_CLEAN': 'Dry Clean',
    'SEPATU': 'Cuci Sepatu',
    'KARPET': 'Cuci Karpet',
    'SELIMUT': 'Cuci Selimut',
  };

  return serviceTypes[serviceType] || serviceType;
}
