import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for customer update
const customerUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  phone_number: z.string().regex(/^[0-9+\-\s()]+$/, 'Invalid phone number format').optional(),
  email: z.string().email('Invalid email format').optional().nullable(),
  address: z.string().max(500, 'Address too long').optional().nullable(),
  behavior_frequency_score: z.number().min(0).max(1).optional(),
  behavior_preference_score: z.number().min(0).max(1).optional(),
  behavior_seasonal_bias: z.number().min(0).max(1).optional(),
});

// GET /api/customers/[id] - Get customer by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const customer = await prisma.customer.findUnique({
      where: { id },
      include: {
        transactions: {
          orderBy: { transaction_date: 'desc' },
          take: 10, // Last 10 transactions
          select: {
            id: true,
            service_type: true,
            weight_kg: true,
            price: true,
            transaction_date: true,
            status: true,
          }
        },
        _count: {
          select: { transactions: true }
        }
      }
    });

    if (!customer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer not found'
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: customer
    });

  } catch (error) {
    console.error('Error fetching customer:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch customer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// PUT /api/customers/[id] - Update customer
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    // Validate input data
    const validatedData = customerUpdateSchema.parse(body);

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer not found'
        },
        { status: 404 }
      );
    }

    // Check for unique constraints if updating phone or email
    if (validatedData.phone_number && validatedData.phone_number !== existingCustomer.phone_number) {
      const phoneExists = await prisma.customer.findUnique({
        where: { phone_number: validatedData.phone_number }
      });

      if (phoneExists) {
        return NextResponse.json(
          {
            success: false,
            error: 'Phone number already exists'
          },
          { status: 400 }
        );
      }
    }

    if (validatedData.email && validatedData.email !== existingCustomer.email) {
      const emailExists = await prisma.customer.findUnique({
        where: { email: validatedData.email }
      });

      if (emailExists) {
        return NextResponse.json(
          {
            success: false,
            error: 'Email already exists'
          },
          { status: 400 }
        );
      }
    }

    const updatedCustomer = await prisma.customer.update({
      where: { id },
      data: validatedData,
      include: {
        _count: {
          select: { transactions: true }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedCustomer,
      message: 'Customer updated successfully'
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error updating customer:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update customer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// DELETE /api/customers/[id] - Delete customer
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Check if customer exists
    const existingCustomer = await prisma.customer.findUnique({
      where: { id },
      include: {
        _count: {
          select: { transactions: true }
        }
      }
    });

    if (!existingCustomer) {
      return NextResponse.json(
        {
          success: false,
          error: 'Customer not found'
        },
        { status: 404 }
      );
    }

    // Check if customer has transactions
    if (existingCustomer._count.transactions > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Cannot delete customer with existing transactions. Consider archiving instead.'
        },
        { status: 400 }
      );
    }

    await prisma.customer.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Customer deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting customer:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete customer',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
