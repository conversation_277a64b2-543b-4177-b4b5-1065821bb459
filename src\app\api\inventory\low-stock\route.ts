import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * API endpoint to get materials with low stock levels.
 * This endpoint finds materials where current stock is at or below minimum threshold.
 */
export async function GET() {
  try {
    // Get all materials and filter for low stock
    const allMaterials = await prisma.material.findMany({
      select: {
        id: true,
        material_name: true,
        current_stock_unit: true,
        minimum_stock_threshold: true,
        unit_of_measure: true,
      },
    });

    // Filter materials where current stock is at or below minimum threshold
    const lowStockItems = allMaterials.filter((item: {
      id: string;
      material_name: string;
      current_stock_unit: number;
      minimum_stock_threshold: number;
      unit_of_measure: string;
    }) => item.current_stock_unit <= item.minimum_stock_threshold);

    // Map the database fields to the frontend's expected camelCase format.
    const formattedItems = lowStockItems.map((item: {
      id: string;
      material_name: string;
      current_stock_unit: number;
      minimum_stock_threshold: number;
      unit_of_measure: string;
    }) => ({
      id: item.id,
      name: item.material_name,
      currentStock: item.current_stock_unit,
      minStock: item.minimum_stock_threshold,
      unit: item.unit_of_measure,
    }));

    return NextResponse.json(formattedItems);
  } catch (error) {
    console.error('--- DETAILED API ERROR in /api/inventory/low-stock ---');
    console.error('A critical error occurred while fetching data from the database:');
    console.error(error);
    console.error('--- END DETAILED API ERROR ---');

    return NextResponse.json(
      { message: 'Internal Server Error. Please check the server console for detailed logs.' },
      { status: 500 }
    );
  }
}
