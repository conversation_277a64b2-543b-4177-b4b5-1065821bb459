import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';

// This is a simplified mock implementation. 
// In a real scenario, these values would be calculated from the database.
async function getRealTimeKpis() {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  yesterday.setHours(0, 0, 0, 0);

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const revenueResult = await prisma.transactions.aggregate({
    _sum: { total_price: true },
    where: {
      transaction_date: {
        gte: today,
        lt: tomorrow,
      },
    },
  });
  const todayRevenue = revenueResult._sum.total_price || 0;

  const newCustomersToday = await prisma.customers.count({
    where: {
      created_at: {
        gte: today,
        lt: tomorrow,
      },
    },
  });

  const returningCustomersToday = await prisma.transactions.count({
    where: {
      transaction_date: {
        gte: today,
        lt: tomorrow,
      },
      customer: {
        created_at: {
          lt: today,
        },
      },
    },
    distinct: ['customer_id'],
  });

  return {
    transactions: {
      today_revenue: todayRevenue,
    },
    customers: {
      new_customers_today: newCustomersToday,
      returning_customers_today: returningCustomersToday,
    },
    operations: {
      // Mocked for now, as queue logic is not in the DB schema yet
      queue_length: Math.floor(Math.random() * 10), 
      active_machines: 4, 
    },
    materials: {
        // Mocked for now, requires more complex calculation
        usage_rate_per_day: {
            material_name: 'Deterjen Bubuk Wangi',
            daily_usage: 1.5,
            unit: 'kg'
        }
    }
  };
}

export async function GET() {
  try {


    const realTimeKpis = await getRealTimeKpis();
    return NextResponse.json(realTimeKpis);
  } catch (error) {
    console.error('Error fetching real-time KPIs:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
