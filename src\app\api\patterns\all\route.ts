import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/prisma';
import { PatternData } from '../../../../lib/contextual-intelligence/types';

// This is a simplified mock implementation for fetching pattern data.
// In a real scenario, these queries would be more complex and might involve aggregations.
async function getPatternData(): Promise<PatternData> {

  const revenuePatterns = await prisma.revenueTrends.findFirst({
    orderBy: { calculation_date: 'desc' },
  });

  const customerPatterns = await prisma.customerPatterns.findFirst({
    orderBy: { calculation_date: 'desc' },
  });

  const materialPatterns = await prisma.materialUsagePatterns.findFirst({
    orderBy: { calculation_date: 'desc' },
  });

  // Construct the final PatternData object matching the type definition
  const patternData: PatternData = {
    revenue_patterns: {
      daily_average: Number(revenuePatterns?.daily_average) || 1350000,
      growth_trend: revenuePatterns?.growth_trend || 'stable',
      seasonal_factor_average: Number(revenuePatterns?.seasonal_factor_average) || 1.0,
      hourly_distribution: { '09:00': 0.1, '14:00': 0.2, '19:00': 0.15 }, // Mocked
    },
    customer_patterns: {
      average_loyalty_score: Number(customerPatterns?.average_loyalty_score) || 0.7,
      segment_distribution: customerPatterns?.segment_distribution as Record<string, number> || { 'premium': 0.15, 'regular': 0.85 },
    },
    material_patterns: {
      average_efficiency_score: Number(materialPatterns?.average_efficiency_score) || 0.88,
      common_combinations: materialPatterns?.common_combinations as string[][] || [['detergent', 'softener']],
    },
  };

  return patternData;
}

export async function GET() {
  try {
    const patternData = await getPatternData();
    return NextResponse.json(patternData);
  } catch (error) {
    console.error('Error fetching pattern data:', error);
    // Check for specific errors, e.g., JSON parsing errors
    if (error instanceof SyntaxError) {
      return NextResponse.json({ error: 'Failed to parse pattern data from DB' }, { status: 500 });
    }
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
