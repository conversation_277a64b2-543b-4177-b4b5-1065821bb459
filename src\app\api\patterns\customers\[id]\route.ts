import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

// GET /api/patterns/customers/[id] - Get specific customer pattern
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const pattern = await prisma.customerPattern.findUnique({
      where: { customer_id: id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone_number: true,
            email: true,
            registration_date: true,
            last_transaction_date: true
          }
        }
      }
    });

    if (!pattern) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Customer pattern not found' 
        },
        { status: 404 }
      );
    }

    const patternWithInsights = {
      ...pattern,
      insights: generateCustomerInsights(pattern),
      recommendations: generateCustomerRecommendations(pattern)
    };

    return NextResponse.json({
      success: true,
      data: patternWithInsights
    });

  } catch (error) {
    console.error('Error fetching customer pattern:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch customer pattern',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function generateCustomerInsights(pattern: any): string[] {
  const insights: string[] = [];

  // Frequency insights
  if (pattern.calculated_frequency > 2) {
    insights.push(`High-frequency customer with ${pattern.calculated_frequency.toFixed(1)} transactions per month`);
  } else if (pattern.calculated_frequency < 0.5) {
    insights.push(`Low-frequency customer with only ${pattern.calculated_frequency.toFixed(1)} transactions per month`);
  }

  // Trend insights
  if (pattern.frequency_trend === 'increasing') {
    insights.push('Customer activity is increasing over time');
  } else if (pattern.frequency_trend === 'decreasing') {
    insights.push('Customer activity is declining - may need attention');
  }

  // Loyalty insights
  if (pattern.loyalty_score > 0.8) {
    insights.push('Highly loyal customer with consistent usage');
  } else if (pattern.loyalty_score < 0.3) {
    insights.push('Low loyalty score - customer may be at risk of churning');
  }

  // Value insights
  if (pattern.value_score > 0.8) {
    insights.push('High-value customer with above-average spending');
  }

  // Predictability insights
  if (pattern.predictability_score > 0.7) {
    insights.push('Highly predictable customer - good for planning');
  } else if (pattern.predictability_score < 0.3) {
    insights.push('Unpredictable usage pattern - irregular customer');
  }

  // Recency insights
  if (pattern.last_transaction_days_ago > 60) {
    insights.push(`Customer hasn't visited in ${pattern.last_transaction_days_ago} days - may need re-engagement`);
  }

  return insights;
}

function generateCustomerRecommendations(pattern: any): string[] {
  const recommendations: string[] = [];

  // Based on loyalty score
  if (pattern.loyalty_score > 0.8) {
    recommendations.push('Consider offering loyalty rewards or VIP treatment');
  } else if (pattern.loyalty_score < 0.3) {
    recommendations.push('Implement retention strategy - offer discounts or special services');
  }

  // Based on frequency trend
  if (pattern.frequency_trend === 'decreasing') {
    recommendations.push('Reach out with personalized offers to re-engage');
  } else if (pattern.frequency_trend === 'increasing') {
    recommendations.push('Capitalize on growing engagement with upselling opportunities');
  }

  // Based on value score
  if (pattern.value_score > 0.8) {
    recommendations.push('Offer premium services or bulk discounts');
  } else if (pattern.value_score < 0.3) {
    recommendations.push('Focus on value-oriented promotions and budget-friendly options');
  }

  // Based on recency
  if (pattern.last_transaction_days_ago > 30) {
    recommendations.push('Send re-engagement campaign or "we miss you" offer');
  }

  // Based on predictability
  if (pattern.predictability_score > 0.7) {
    recommendations.push('Set up automated reminders based on their usage pattern');
  }

  return recommendations;
}
