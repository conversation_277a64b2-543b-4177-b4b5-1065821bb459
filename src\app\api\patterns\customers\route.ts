import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { z } from 'zod';

const prisma = new PrismaClient();

// GET /api/patterns/customers - Get customer patterns
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const customer_id = searchParams.get('customer_id');
    const min_confidence = parseFloat(searchParams.get('min_confidence') || '0');
    const sortBy = searchParams.get('sortBy') || 'calculated_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      confidence_score: {
        gte: min_confidence
      }
    };

    if (customer_id) {
      where.customer_id = customer_id;
    }

    // Build orderBy clause
    const orderBy = { [sortBy]: sortOrder };

    const [patterns, total] = await Promise.all([
      prisma.customerPattern.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              phone_number: true,
              email: true,
              registration_date: true
            }
          }
        }
      }),
      prisma.customerPattern.count({ where })
    ]);

    // Add insights to each pattern
    const patternsWithInsights = patterns.map(pattern => ({
      ...pattern,
      insights: generateCustomerInsights(pattern)
    }));

    return NextResponse.json({
      success: true,
      data: patternsWithInsights,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching customer patterns:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch customer patterns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/patterns/customers/[id] - Get specific customer pattern
export async function getCustomerPattern(customerId: string) {
  try {
    const pattern = await prisma.customerPattern.findUnique({
      where: { customer_id: customerId },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            phone_number: true,
            email: true,
            registration_date: true,
            last_transaction_date: true
          }
        }
      }
    });

    if (!pattern) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Customer pattern not found' 
        },
        { status: 404 }
      );
    }

    const patternWithInsights = {
      ...pattern,
      insights: generateCustomerInsights(pattern),
      recommendations: generateCustomerRecommendations(pattern)
    };

    return NextResponse.json({
      success: true,
      data: patternWithInsights
    });

  } catch (error) {
    console.error('Error fetching customer pattern:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch customer pattern',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/patterns/customers/calculate - Trigger pattern calculation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const schema = z.object({
      customer_id: z.string().optional(),
      force_recalculate: z.boolean().default(false)
    });

    const { customer_id, force_recalculate } = schema.parse(body);

    // Import the calculation service
    const { PatternCalculationService } = await import('@/lib/pattern-analysis/pattern-calculator');
    const calculationService = new PatternCalculationService();

    if (customer_id) {
      // Calculate for specific customer
      const { CustomerPatternAnalyzer } = await import('@/lib/pattern-analysis/customer-patterns');
      const analyzer = new CustomerPatternAnalyzer();
      
      const pattern = await analyzer.analyzeCustomerPattern(customer_id);
      
      if (!pattern) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Insufficient data to calculate pattern for this customer' 
          },
          { status: 400 }
        );
      }

      // Save the pattern
      await prisma.customerPattern.upsert({
        where: { customer_id },
        update: {
          calculated_at: new Date(),
          calculated_frequency: pattern.calculated_frequency,
          frequency_trend: pattern.frequency_trend,
          last_transaction_days_ago: pattern.last_transaction_days_ago,
          preferred_service_type: pattern.preferred_service_type,
          preferred_service_confidence: pattern.preferred_service_confidence,
          average_weight_kg: pattern.average_weight_kg,
          average_spending: pattern.average_spending,
          seasonal_activity_json: pattern.seasonal_activity_json,
          peak_months: pattern.peak_months,
          low_months: pattern.low_months,
          seasonal_variance: pattern.seasonal_variance,
          loyalty_score: pattern.loyalty_score,
          value_score: pattern.value_score,
          predictability_score: pattern.predictability_score,
          confidence_score: pattern.confidence_score,
          data_points_count: pattern.data_points_count,
          analysis_period_days: pattern.analysis_period_days
        },
        create: {
          customer_id,
          calculated_frequency: pattern.calculated_frequency,
          frequency_trend: pattern.frequency_trend,
          last_transaction_days_ago: pattern.last_transaction_days_ago,
          preferred_service_type: pattern.preferred_service_type,
          preferred_service_confidence: pattern.preferred_service_confidence,
          average_weight_kg: pattern.average_weight_kg,
          average_spending: pattern.average_spending,
          seasonal_activity_json: pattern.seasonal_activity_json,
          peak_months: pattern.peak_months,
          low_months: pattern.low_months,
          seasonal_variance: pattern.seasonal_variance,
          loyalty_score: pattern.loyalty_score,
          value_score: pattern.value_score,
          predictability_score: pattern.predictability_score,
          confidence_score: pattern.confidence_score,
          data_points_count: pattern.data_points_count,
          analysis_period_days: pattern.analysis_period_days
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Customer pattern calculated successfully',
        data: pattern
      });

    } else {
      // Calculate for all customers
      const result = await calculationService.calculateCustomerPatterns();
      
      return NextResponse.json({
        success: true,
        message: 'Customer patterns calculation triggered',
        data: result
      });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error calculating customer patterns:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to calculate customer patterns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function generateCustomerInsights(pattern: any): string[] {
  const insights: string[] = [];

  // Frequency insights
  if (pattern.calculated_frequency > 2) {
    insights.push(`High-frequency customer with ${pattern.calculated_frequency.toFixed(1)} transactions per month`);
  } else if (pattern.calculated_frequency < 0.5) {
    insights.push(`Low-frequency customer with only ${pattern.calculated_frequency.toFixed(1)} transactions per month`);
  }

  // Trend insights
  if (pattern.frequency_trend === 'increasing') {
    insights.push('Customer activity is increasing over time');
  } else if (pattern.frequency_trend === 'decreasing') {
    insights.push('Customer activity is declining - may need attention');
  }

  // Loyalty insights
  if (pattern.loyalty_score > 0.8) {
    insights.push('Highly loyal customer with consistent usage');
  } else if (pattern.loyalty_score < 0.3) {
    insights.push('Low loyalty score - customer may be at risk of churning');
  }

  // Value insights
  if (pattern.value_score > 0.8) {
    insights.push('High-value customer with above-average spending');
  }

  // Predictability insights
  if (pattern.predictability_score > 0.7) {
    insights.push('Highly predictable customer - good for planning');
  } else if (pattern.predictability_score < 0.3) {
    insights.push('Unpredictable usage pattern - irregular customer');
  }

  // Recency insights
  if (pattern.last_transaction_days_ago > 60) {
    insights.push(`Customer hasn't visited in ${pattern.last_transaction_days_ago} days - may need re-engagement`);
  }

  return insights;
}

function generateCustomerRecommendations(pattern: any): string[] {
  const recommendations: string[] = [];

  // Based on loyalty score
  if (pattern.loyalty_score > 0.8) {
    recommendations.push('Consider offering loyalty rewards or VIP treatment');
  } else if (pattern.loyalty_score < 0.3) {
    recommendations.push('Implement retention strategy - offer discounts or special services');
  }

  // Based on frequency trend
  if (pattern.frequency_trend === 'decreasing') {
    recommendations.push('Reach out with personalized offers to re-engage');
  } else if (pattern.frequency_trend === 'increasing') {
    recommendations.push('Capitalize on growing engagement with upselling opportunities');
  }

  // Based on value score
  if (pattern.value_score > 0.8) {
    recommendations.push('Offer premium services or bulk discounts');
  } else if (pattern.value_score < 0.3) {
    recommendations.push('Focus on value-oriented promotions and budget-friendly options');
  }

  // Based on recency
  if (pattern.last_transaction_days_ago > 30) {
    recommendations.push('Send re-engagement campaign or "we miss you" offer');
  }

  // Based on predictability
  if (pattern.predictability_score > 0.7) {
    recommendations.push('Set up automated reminders based on their usage pattern');
  }

  return recommendations;
}
