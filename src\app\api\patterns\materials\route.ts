import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { z } from 'zod';

const prisma = new PrismaClient();

// GET /api/patterns/materials - Get material usage patterns
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const material_id = searchParams.get('material_id');
    const reorder_only = searchParams.get('reorder_only') === 'true';
    const min_confidence = parseFloat(searchParams.get('min_confidence') || '0');
    const sortBy = searchParams.get('sortBy') || 'calculated_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      confidence_score: {
        gte: min_confidence
      }
    };

    if (material_id) {
      where.material_id = material_id;
    }

    if (reorder_only) {
      where.reorder_recommendation = true;
    }

    // Build orderBy clause
    const orderBy = { [sortBy]: sortOrder };

    const [patterns, total] = await Promise.all([
      prisma.materialUsagePattern.findMany({
        where,
        skip,
        take: limit,
        orderBy,
        include: {
          material: {
            select: {
              id: true,
              material_name: true,
              current_stock_unit: true,
              unit_of_measure: true,
              category: true,
              minimum_stock_threshold: true,
              cost_per_unit: true
            }
          }
        }
      }),
      prisma.materialUsagePattern.count({ where })
    ]);

    // Add insights to each pattern
    const patternsWithInsights = patterns.map(pattern => ({
      ...pattern,
      insights: generateMaterialInsights(pattern),
      stock_status: determineStockStatus(pattern)
    }));

    return NextResponse.json({
      success: true,
      data: patternsWithInsights,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching material patterns:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch material patterns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/patterns/materials/calculate - Trigger material pattern calculation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const schema = z.object({
      material_id: z.string().optional(),
      force_recalculate: z.boolean().default(false)
    });

    const { material_id, force_recalculate } = schema.parse(body);

    // Import the calculation service
    const { PatternCalculationService } = await import('@/lib/pattern-analysis/pattern-calculator');
    const calculationService = new PatternCalculationService();

    if (material_id) {
      // Calculate for specific material
      const { MaterialPatternAnalyzer } = await import('@/lib/pattern-analysis/material-patterns');
      const analyzer = new MaterialPatternAnalyzer();

      const pattern = await analyzer.analyzeMaterialPattern(material_id);

      if (!pattern) {
        return NextResponse.json(
          {
            success: false,
            error: 'Insufficient data to calculate pattern for this material'
          },
          { status: 400 }
        );
      }

      // Save the pattern
      await prisma.materialUsagePattern.upsert({
        where: { material_id },
        update: {
          calculated_at: new Date(),
          average_consumption_rate: pattern.average_consumption_rate,
          consumption_trend: pattern.consumption_trend,
          peak_usage_day_type: pattern.peak_usage_day_type,
          peak_usage_time: pattern.peak_usage_time,
          seasonal_adjustment_json: pattern.seasonal_adjustment_json,
          peak_consumption_months: pattern.peak_consumption_months,
          low_consumption_months: pattern.low_consumption_months,
          seasonal_multiplier: pattern.seasonal_multiplier,
          cost_efficiency_score: pattern.cost_efficiency_score,
          usage_efficiency_score: pattern.usage_efficiency_score,
          waste_indicator: pattern.waste_indicator,
          predicted_days_to_empty: pattern.predicted_days_to_empty,
          reorder_recommendation: pattern.reorder_recommendation,
          optimal_stock_level: pattern.optimal_stock_level,
          confidence_score: pattern.confidence_score,
          data_points_count: pattern.data_points_count,
          analysis_period_days: pattern.analysis_period_days
        },
        create: {
          material_id,
          average_consumption_rate: pattern.average_consumption_rate,
          consumption_trend: pattern.consumption_trend,
          peak_usage_day_type: pattern.peak_usage_day_type,
          peak_usage_time: pattern.peak_usage_time,
          seasonal_adjustment_json: pattern.seasonal_adjustment_json,
          peak_consumption_months: pattern.peak_consumption_months,
          low_consumption_months: pattern.low_consumption_months,
          seasonal_multiplier: pattern.seasonal_multiplier,
          cost_efficiency_score: pattern.cost_efficiency_score,
          usage_efficiency_score: pattern.usage_efficiency_score,
          waste_indicator: pattern.waste_indicator,
          predicted_days_to_empty: pattern.predicted_days_to_empty,
          reorder_recommendation: pattern.reorder_recommendation,
          optimal_stock_level: pattern.optimal_stock_level,
          confidence_score: pattern.confidence_score,
          data_points_count: pattern.data_points_count,
          analysis_period_days: pattern.analysis_period_days
        }
      });

      return NextResponse.json({
        success: true,
        message: 'Material pattern calculated successfully',
        data: pattern
      });

    } else {
      // Calculate for all materials
      const result = await calculationService.calculateMaterialPatterns();

      return NextResponse.json({
        success: true,
        message: 'Material patterns calculation triggered',
        data: result
      });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error calculating material patterns:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to calculate material patterns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function generateMaterialInsights(pattern: any): string[] {
  const insights: string[] = [];

  // Consumption trend insights
  if (pattern.consumption_trend === 'increasing') {
    insights.push('Material consumption is increasing - consider bulk purchasing');
  } else if (pattern.consumption_trend === 'decreasing') {
    insights.push('Material consumption is decreasing - review usage efficiency');
  }

  // Stock level insights
  if (pattern.predicted_days_to_empty <= 7) {
    insights.push(`Critical: Only ${pattern.predicted_days_to_empty} days of stock remaining`);
  } else if (pattern.predicted_days_to_empty <= 14) {
    insights.push(`Warning: ${pattern.predicted_days_to_empty} days of stock remaining`);
  }

  // Efficiency insights
  if (pattern.cost_efficiency_score > 0.8) {
    insights.push('Excellent cost efficiency - usage is optimized');
  } else if (pattern.cost_efficiency_score < 0.5) {
    insights.push('Poor cost efficiency - review usage practices');
  }

  if (pattern.usage_efficiency_score > 0.8) {
    insights.push('Optimal usage efficiency per kg of laundry');
  } else if (pattern.usage_efficiency_score < 0.5) {
    insights.push('Usage efficiency below optimal - may be over/under-using');
  }

  // Waste insights
  if (pattern.waste_indicator > 0.5) {
    insights.push('High usage variance detected - potential waste or inconsistent practices');
  }

  // Seasonal insights
  if (pattern.seasonal_multiplier > 1.5) {
    insights.push('Strong seasonal variation - plan inventory for peak periods');
  }

  // Reorder insights
  if (pattern.reorder_recommendation) {
    insights.push('Reorder recommended based on current consumption patterns');
  }

  return insights;
}

function determineStockStatus(pattern: any): string {
  if (pattern.predicted_days_to_empty <= 7) {
    return 'CRITICAL';
  } else if (pattern.predicted_days_to_empty <= 14) {
    return 'LOW';
  } else if (pattern.predicted_days_to_empty <= 30) {
    return 'MEDIUM';
  } else {
    return 'HIGH';
  }
}
