import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@/generated/prisma';
import { z } from 'zod';

const prisma = new PrismaClient();

// GET /api/patterns/revenue - Get revenue trend patterns
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '30'); // Default to 30 days
    const date_from = searchParams.get('date_from');
    const date_to = searchParams.get('date_to');
    const trend_type = searchParams.get('trend_type'); // 'peak', 'high', 'normal', 'low', 'valley'
    const peak_status = searchParams.get('peak_status'); // 'daily_peak', 'weekly_peak', 'monthly_peak'
    const min_confidence = parseFloat(searchParams.get('min_confidence') || '0');
    const sortBy = searchParams.get('sortBy') || 'date';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      confidence_score: {
        gte: min_confidence
      }
    };

    if (date_from || date_to) {
      where.date = {};
      if (date_from) {
        where.date.gte = new Date(date_from);
      }
      if (date_to) {
        where.date.lte = new Date(date_to);
      }
    }

    if (trend_type) {
      where.revenue_trend = trend_type;
    }

    if (peak_status) {
      where.peak_status = peak_status;
    }

    // Build orderBy clause
    const orderBy = { [sortBy]: sortOrder };

    const [trends, total] = await Promise.all([
      prisma.revenueTrend.findMany({
        where,
        skip,
        take: limit,
        orderBy
      }),
      prisma.revenueTrend.count({ where })
    ]);

    // Add insights and analytics
    const trendsWithInsights = trends.map(trend => ({
      ...trend,
      insights: generateRevenueInsights(trend)
    }));

    // Calculate summary statistics
    const summary = calculateRevenueSummary(trends);

    return NextResponse.json({
      success: true,
      data: trendsWithInsights,
      summary,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching revenue patterns:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch revenue patterns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// POST /api/patterns/revenue/calculate - Trigger revenue pattern calculation
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    const schema = z.object({
      start_date: z.string().datetime().optional(),
      end_date: z.string().datetime().optional(),
      force_recalculate: z.boolean().default(false)
    });

    const { start_date, end_date, force_recalculate } = schema.parse(body);

    // Import the calculation service
    const { PatternCalculationService } = await import('@/lib/pattern-analysis/pattern-calculator');
    const calculationService = new PatternCalculationService();

    if (start_date && end_date) {
      // Calculate for specific date range
      const { RevenuePatternAnalyzer } = await import('@/lib/pattern-analysis/revenue-patterns');
      const analyzer = new RevenuePatternAnalyzer();

      const trends = await analyzer.analyzeRevenueForDateRange(
        new Date(start_date),
        new Date(end_date)
      );

      // Save the trends
      let recordsUpdated = 0;
      for (const trend of trends) {
        await prisma.revenueTrend.upsert({
          where: { date: trend.date },
          update: {
            daily_revenue: trend.daily_revenue,
            daily_transaction_count: trend.daily_transaction_count,
            daily_avg_transaction: trend.daily_avg_transaction,
            daily_weight_total: trend.daily_weight_total,
            weekly_avg_revenue: trend.weekly_avg_revenue,
            weekly_transaction_count: trend.weekly_transaction_count,
            week_day_rank: trend.week_day_rank,
            monthly_avg_revenue: trend.monthly_avg_revenue,
            monthly_transaction_count: trend.monthly_transaction_count,
            month_day_rank: trend.month_day_rank,
            revenue_trend: trend.revenue_trend,
            peak_status: trend.peak_status,
            growth_rate: trend.growth_rate,
            day_type: trend.day_type,
            weather_context: trend.weather_context,
            seasonal_factor: trend.seasonal_factor,
            confidence_score: trend.confidence_score,
            data_completeness: trend.data_completeness
          },
          create: {
            date: trend.date,
            daily_revenue: trend.daily_revenue,
            daily_transaction_count: trend.daily_transaction_count,
            daily_avg_transaction: trend.daily_avg_transaction,
            daily_weight_total: trend.daily_weight_total,
            weekly_avg_revenue: trend.weekly_avg_revenue,
            weekly_transaction_count: trend.weekly_transaction_count,
            week_day_rank: trend.week_day_rank,
            monthly_avg_revenue: trend.monthly_avg_revenue,
            monthly_transaction_count: trend.monthly_transaction_count,
            month_day_rank: trend.month_day_rank,
            revenue_trend: trend.revenue_trend,
            peak_status: trend.peak_status,
            growth_rate: trend.growth_rate,
            day_type: trend.day_type,
            weather_context: trend.weather_context,
            seasonal_factor: trend.seasonal_factor,
            confidence_score: trend.confidence_score,
            data_completeness: trend.data_completeness
          }
        });
        recordsUpdated++;
      }

      return NextResponse.json({
        success: true,
        message: 'Revenue trends calculated successfully',
        data: {
          records_processed: trends.length,
          records_updated: recordsUpdated,
          date_range: { start_date, end_date }
        }
      });

    } else {
      // Calculate default range (last 90 days)
      const result = await calculationService.calculateRevenueTrends();

      return NextResponse.json({
        success: true,
        message: 'Revenue trends calculation triggered',
        data: result
      });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation failed',
          details: error.errors
        },
        { status: 400 }
      );
    }

    console.error('Error calculating revenue patterns:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to calculate revenue patterns',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

function generateRevenueInsights(trend: any): string[] {
  const insights: string[] = [];

  // Revenue trend insights
  if (trend.revenue_trend === 'peak') {
    insights.push('Peak revenue day - exceptional performance');
  } else if (trend.revenue_trend === 'valley') {
    insights.push('Valley revenue day - investigate potential issues');
  }

  // Peak status insights
  if (trend.peak_status === 'monthly_peak') {
    insights.push('Monthly peak performance - best day of the month');
  } else if (trend.peak_status === 'weekly_peak') {
    insights.push('Weekly peak performance - best day of the week');
  }

  // Growth rate insights
  if (trend.growth_rate > 0.2) {
    insights.push(`Strong growth: ${(trend.growth_rate * 100).toFixed(1)}% increase from last week`);
  } else if (trend.growth_rate < -0.2) {
    insights.push(`Concerning decline: ${Math.abs(trend.growth_rate * 100).toFixed(1)}% decrease from last week`);
  }

  // Transaction insights
  if (trend.daily_transaction_count === 0) {
    insights.push('No transactions recorded - check for operational issues');
  } else if (trend.daily_transaction_count > trend.weekly_avg_revenue / 7 * 1.5) {
    insights.push('High transaction volume day');
  }

  // Average transaction insights
  if (trend.daily_avg_transaction > trend.monthly_avg_revenue / 30 * 1.3) {
    insights.push('Above-average transaction value - premium services or bulk orders');
  }

  // Day type insights
  if (trend.day_type === 'WEEKEND' && trend.daily_revenue > trend.weekly_avg_revenue) {
    insights.push('Strong weekend performance - good work-life balance for customers');
  }

  // Confidence insights
  if (trend.confidence_score < 0.5) {
    insights.push('Low confidence in data - may need verification');
  }

  return insights;
}

function calculateRevenueSummary(trends: any[]) {
  if (trends.length === 0) {
    return {
      total_revenue: 0,
      average_daily_revenue: 0,
      total_transactions: 0,
      average_daily_transactions: 0,
      peak_days: 0,
      valley_days: 0,
      growth_trend: 'stable'
    };
  }

  const total_revenue = trends.reduce((sum, t) => sum + t.daily_revenue, 0);
  const total_transactions = trends.reduce((sum, t) => sum + t.daily_transaction_count, 0);
  const peak_days = trends.filter(t => t.revenue_trend === 'peak').length;
  const valley_days = trends.filter(t => t.revenue_trend === 'valley').length;

  const average_daily_revenue = total_revenue / trends.length;
  const average_daily_transactions = total_transactions / trends.length;

  // Calculate overall growth trend
  const firstHalf = trends.slice(0, Math.floor(trends.length / 2));
  const secondHalf = trends.slice(Math.floor(trends.length / 2));

  const firstHalfAvg = firstHalf.reduce((sum, t) => sum + t.daily_revenue, 0) / firstHalf.length;
  const secondHalfAvg = secondHalf.reduce((sum, t) => sum + t.daily_revenue, 0) / secondHalf.length;

  let growth_trend = 'stable';
  if (secondHalfAvg > firstHalfAvg * 1.1) {
    growth_trend = 'increasing';
  } else if (secondHalfAvg < firstHalfAvg * 0.9) {
    growth_trend = 'decreasing';
  }

  return {
    total_revenue,
    average_daily_revenue,
    total_transactions,
    average_daily_transactions,
    peak_days,
    valley_days,
    growth_trend,
    period_days: trends.length
  };
}
