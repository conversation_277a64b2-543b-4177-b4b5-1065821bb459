import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { TransactionStatus } from '@/generated/prisma/client'; // Ensure enum is imported correctly

/**
 * API endpoint to get transactions that are overdue for pickup.
 * An order is overdue if its status is READY and the pickup_date has passed.
 */
export async function GET() {
  try {
    const now = new Date();

    const overduePickups = await prisma.transaction.findMany({
      where: {
        status: TransactionStatus.READY,
        pickup_date: {
          lt: now, // Less than the current date/time
          not: null, // Ensure pickup_date is set
        },
      },
      include: {
        customer: {
          select: {
            name: true,
            phone_number: true,
          },
        },
      },
      orderBy: {
        pickup_date: 'asc', // Show the oldest overdue items first
      },
    });

    // Format the data for the frontend if needed, or return directly
    // For now, we'll map to a slightly more frontend-friendly structure
    const formattedOverduePickups = overduePickups.map(transaction => ({
      transactionId: transaction.id,
      serviceType: transaction.service_type,
      weightKg: transaction.weight_kg,
      pickupDate: transaction.pickup_date,
      customerName: transaction.customer.name,
      customerPhone: transaction.customer.phone_number,
      daysOverdue: Math.floor((now.getTime() - (transaction.pickup_date?.getTime() || now.getTime())) / (1000 * 60 * 60 * 24))
    }));

    return NextResponse.json(formattedOverduePickups);
  } catch (error) {
    console.error('--- DETAILED API ERROR in /api/transactions/overdue-pickups ---');
    console.error('A critical error occurred while fetching overdue pickups:');
    console.error(error);
    console.error('--- END DETAILED API ERROR ---');

    return NextResponse.json(
      { message: 'Internal Server Error. Please check the server console for detailed logs.' },
      { status: 500 }
    );
  }
}
