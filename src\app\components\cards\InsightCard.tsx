"use client";

import React, { useState } from 'react';
import { Insight } from '@/app/types';
import { ChevronDown, ChevronUp, AlertCircle, CheckCircle, Info, Zap, Clock, Target, BarChart2 } from 'lucide-react';

interface InsightCardProps {
  insight: Insight;
}

const InsightCard: React.FC<InsightCardProps> = ({ insight }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const priorityConfig = {
    high: {
      label: 'High Priority',
      Icon: AlertCircle,
      tagClasses: 'text-red-400 bg-red-500/10 border border-red-500/30',
      iconColor: 'text-red-400',
    },
    medium: {
      label: 'Medium Priority',
      Icon: Zap,
      tagClasses: 'text-yellow-400 bg-yellow-500/10 border border-yellow-500/30',
      iconColor: 'text-yellow-400',
    },
    low: {
      label: 'Low Priority',
      Icon: Info,
      tagClasses: 'text-sky-400 bg-sky-500/10 border border-sky-500/30',
      iconColor: 'text-sky-400',
    },
    default: {
      label: 'General Info',
      Icon: CheckCircle,
      tagClasses: 'text-gray-400 bg-gray-500/10 border border-gray-500/30',
      iconColor: 'text-gray-400',
    },
  };

  const config = priorityConfig[insight.priority] || priorityConfig.default;

  const detailItems = [
    { label: 'Konteks', value: insight.contextText, Icon: Info, color: 'text-sky-400' },
    { label: 'Saran Aksi', value: insight.action, Icon: Target, color: 'text-green-400' },
    { label: 'Potensi Dampak', value: insight.impact, Icon: BarChart2, color: 'text-purple-400' },
    { label: 'Estimasi Waktu', value: insight.timeEstimate, Icon: Clock, color: 'text-orange-400' },
  ].filter(item => item.value);

  return (
    <div
      className="bg-gray-800/50 border border-gray-700/60 rounded-xl transition-all hover:border-sky-500/80 hover:bg-gray-800/80"
    >
      {/* Collapsed View */}
      <div
        className="flex justify-between items-center p-5 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && setIsExpanded(!isExpanded)}
        aria-expanded={isExpanded}
        aria-controls={`insight-details-${insight.id}`}
      >
        <div className="flex-1 min-w-0">
          <h3 className="text-base font-semibold text-white truncate">{insight.title}</h3>
          <p className="text-sm text-gray-400 mt-1 truncate">{insight.shortDescription}</p>
        </div>

        <div className="flex items-center gap-4 ml-4 flex-shrink-0">
          <div className={`flex items-center gap-2 text-xs font-medium rounded-full px-2.5 py-1 ${config.tagClasses}`}>
            <config.Icon size={14} />
            <span>{config.label}</span>
          </div>
          {isExpanded ? <ChevronUp size={20} className="text-gray-400" /> : <ChevronDown size={20} className="text-gray-400" />}
        </div>
      </div>

      {/* Expanded View */}
      {isExpanded && (
        <div id={`insight-details-${insight.id}`} className="px-5 pb-5 pt-4 border-t border-gray-700/60 animate-fadeIn">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
            {detailItems.map(({ label, value, Icon, color }) => (
              <div key={label} className="flex items-start gap-3">
                <Icon size={16} className={`${color} mt-1 flex-shrink-0`} />
                <div>
                  <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider">{label}</p>
                  <p className="text-sm text-white">{value}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default InsightCard;
