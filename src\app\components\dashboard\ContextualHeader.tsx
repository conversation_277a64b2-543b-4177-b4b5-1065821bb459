"use client";

import React from 'react';
import { useDashboard } from '@/app/context/DashboardContext';

const ContextualHeader = () => {
  const { currentConfig, currentTime } = useDashboard();
  const { Icon } = currentConfig;

  return (
    <header className={`bg-gradient-to-r ${currentConfig.color} text-white p-6 rounded-lg mb-6 shadow-lg animate-fadeIn mt-6`}>
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Icon className="w-6 h-6" />
            <h1 className="text-2xl font-bold">{currentConfig.title}</h1>
          </div>
          <p className="text-white/90">{currentConfig.subtitle}</p>
          <p className="text-sm text-white/70 mt-1">
            {currentTime.toLocaleString('id-ID', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </p>
        </div>
        <div className="text-right">
          <div className="text-3xl font-bold">LaundrySense</div>
          <div className="text-sm text-white/70">Laundry Berkah Jaya</div>
        </div>
      </div>
    </header>
  );
};

export default ContextualHeader;
