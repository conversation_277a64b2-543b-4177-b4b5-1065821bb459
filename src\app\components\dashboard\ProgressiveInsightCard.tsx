"use client";

import React from 'react';
import { ChevronRight } from 'lucide-react';
import type { GeneratedInsight as Insight, InsightPriority } from '@/lib/contextual-intelligence/types/insight-types';

interface ProgressiveInsightCardProps {
  insight: Insight;
  isExpanded: boolean;
  onToggle: (id: string) => void;
}

const priorityStyles: Record<InsightPriority, { border: string; bg: string; }> = {
  high: { border: 'border-l-red-500', bg: 'bg-red-50/70' },
  medium: { border: 'border-l-yellow-400', bg: 'bg-yellow-50/70' },
  low: { border: 'border-l-green-500', bg: 'bg-green-50/70' },
  normal: { border: 'border-l-blue-500', bg: 'bg-blue-50/70' },
  critical: { border: 'border-l-purple-600', bg: 'bg-purple-100/70' },
};

const ProgressiveInsightCard: React.FC<ProgressiveInsightCardProps> = ({ insight, isExpanded, onToggle }) => {
  const styles = priorityStyles[insight.priority];

  return (
    <div className={`border-l-4 p-4 rounded-r-lg cursor-pointer transition-all duration-300 ${styles.border} ${styles.bg} ${isExpanded ? 'shadow-lg scale-[1.01]' : 'hover:shadow-md'}`}>
      <div onClick={() => onToggle(insight.id)} className="flex justify-between items-start">
        <div className="flex-1 pr-4">
          <h3 className="font-semibold text-gray-800">{insight.title}</h3>
          <p className="text-sm text-gray-600 mt-1">{insight.context}</p>
        </div>
        <ChevronRight className={`w-5 h-5 text-gray-400 transition-transform duration-300 ${isExpanded ? 'rotate-90' : ''}`} />
      </div>
      
      {isExpanded && (
        <div className="mt-4 space-y-3 animate-fadeIn">
          <div className="bg-white/80 p-3 rounded-md border border-gray-200/90">
            <h4 className="font-medium text-sm text-gray-700">Recommended Action:</h4>
            <p className="text-sm text-gray-900 mt-1">{insight.action}</p>
          </div>
          <div className="flex justify-between text-xs text-gray-500 px-1">
            <span>Impact: {insight.impact}</span>
            <span>Est. Time: {insight.timeEstimate}</span>
          </div>
          <button className="w-full bg-gray-900 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-gray-800 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-900">
            Take Action
          </button>
        </div>
      )}
    </div>
  );
};

export default ProgressiveInsightCard;
