"use client";

import React from 'react';
import { useDashboard } from '@/app/context/DashboardContext';

const QuickMetrics: React.FC = () => {
  const { kpis } = useDashboard();

  return (
    <section className="mb-6 animate-fadeIn">
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {kpis.map((kpi) => (
          <div
            key={kpi.id}
            className="bg-white rounded-lg shadow-sm p-4 border border-gray-200/80 hover:shadow-md hover:-translate-y-1 transition-all duration-300"
          >
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-gray-600">{kpi.label}</p>
              <kpi.icon className={`w-5 h-5 ${kpi.color}`} />
            </div>
            <p className="text-2xl font-bold text-gray-800 mt-2">{kpi.value}</p>
            <p className="text-xs text-gray-500 mt-1">{kpi.trend}</p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default QuickMetrics;
