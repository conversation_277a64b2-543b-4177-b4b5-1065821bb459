"use client";

import React from 'react';
import { useDashboard } from '@/app/context/DashboardContext';
import { ShieldCheck, ShieldAlert, ShieldX } from 'lucide-react';

const DataQualityIndicator: React.FC = () => {
  const { context } = useDashboard();
  const dataQualityScore = context.dataQualityScore;

  const getConfig = () => {
    if (dataQualityScore >= 85) {
      return {
        Icon: ShieldCheck,
        className: 'text-green-400',
        title: `Data Quality: High (${dataQualityScore}%)`,
      };
    } else if (dataQualityScore >= 60) {
      return {
        Icon: ShieldAlert,
        className: 'text-yellow-400',
        title: `Data Quality: Medium (${dataQualityScore}%)`,
      };
    } else if (dataQualityScore > 0) {
      return {
        Icon: ShieldX,
        className: 'text-red-400',
        title: `Data Quality: Low (${dataQualityScore}%)`,
      };
    }
    return {
      Icon: ShieldCheck, // Default or N/A
      className: 'text-gray-500',
      title: 'Data Quality: N/A',
    };
  };

  const { Icon, className, title } = getConfig();

  return (
    <div 
      title={title} 
      className="h-9 w-9 flex items-center justify-center bg-gray-700/50 border border-gray-600/80 rounded-full"
    >
      <Icon size={18} className={className} />
    </div>
  );
};

export default DataQualityIndicator;
