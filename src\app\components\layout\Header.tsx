"use client";

import React from 'react';
import { useDashboard } from '@/app/context/DashboardContext';
import {
  Sun, Moon, CloudSun, CloudMoon, Zap, TrendingUp, AlertTriangle, Briefcase, Settings2, Coffee
} from 'lucide-react';
import DataQualityIndicator from './DataQualityIndicator'; // Import the new component

// Define a type for the icon configuration
type IconConfig = {
  title: string;
  Icon: React.ElementType;
  className: string;
};

const StatusIcon: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div title={title} className="h-9 w-9 flex items-center justify-center bg-gray-700/50 border border-gray-600/80 rounded-full">
    {children}
  </div>
);

const Header: React.FC = () => {
  const { context } = useDashboard();
  const { currentContextObject } = context;

  const contextIcons: {
    businessCycle: { [key: string]: IconConfig };
    userBehavior: { [key: string]: IconConfig };
  } = {
    businessCycle: {
      peak_season: { title: 'Musim Ramai', Icon: TrendingUp, className: 'text-green-400' },
      low_season: { title: 'Musim Sepi', Icon: Coffee, className: 'text-blue-400' },
      normal_season: { title: 'Musim Normal', Icon: Briefcase, className: 'text-indigo-400' },
      default: { title: 'Siklus Bisnis', Icon: Briefcase, className: 'text-gray-400' },
    },
    userBehavior: {
      stress_mode: { title: 'Mode Stres', Icon: AlertTriangle, className: 'text-red-400' },
      growth_mode: { title: 'Mode Pertumbuhan', Icon: TrendingUp, className: 'text-teal-400' },
      normal_mode: { title: 'Mode Normal', Icon: Settings2, className: 'text-sky-400' },
      default: { title: 'Perilaku Pengguna', Icon: Settings2, className: 'text-gray-400' },
    },
  };

  const getIcon = (type: 'businessCycle' | 'userBehavior', key: string) => {
    const config = contextIcons[type][key] || contextIcons[type].default;
    return (
      <StatusIcon title={config.title}>
        <config.Icon size={18} className={config.className} />
      </StatusIcon>
    );
  };

  return (
    <header className="sticky top-0 z-50 bg-gray-900/80 backdrop-blur-lg border-b border-gray-700/60">
      <div className="w-full max-w-7xl mx-auto px-6 sm:px-8 md:px-10">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center gap-3">
            <div className="bg-sky-500/10 p-2 rounded-lg border border-sky-500/30">
              <Zap size={20} className="text-sky-400" />
            </div>
            <span className="text-lg font-bold text-white tracking-tight">
              LaundrySense
            </span>
          </div>

          <div className="flex items-center gap-3">
            {getIcon('businessCycle', currentContextObject.businessCycle)}
            {getIcon('userBehavior', currentContextObject.userBehavior)}
            <DataQualityIndicator />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
