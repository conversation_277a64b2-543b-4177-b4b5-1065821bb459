"use client";

import React from 'react';
import { DashboardProvider } from '@/app/context/DashboardContext';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <DashboardProvider>
      {/* The new design uses a light gray background, applied directly in the main div */}
      <div className="min-h-screen bg-gray-50 font-sans text-gray-900">
        {/* The old Header is removed. The new ContextualHeader will be part of the dashboard page itself */}
        <main className="p-4 sm:p-6 md:p-8">
          {children}
        </main>
      </div>
    </DashboardProvider>
  );
};

export default MainLayout;
