"use client";

import React, { useState } from 'react';
import { useDashboard } from '../context/DashboardContext';
import type { ContextMode } from '../context/DashboardContext';
import ContextualHeader from '../components/dashboard/ContextualHeader';
import QuickMetrics from '../components/dashboard/QuickMetrics';
import ProgressiveInsightCard from '../components/dashboard/ProgressiveInsightCard';

const DashboardPage: React.FC = () => {
  const { insights, contextMode, setContextMode, contextConfig } = useDashboard();
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  const handleToggleCard = (id: string) => {
    setExpandedCard(expandedCard === id ? null : id);
  };

  return (
    <div className="space-y-6">
      <ContextualHeader />
      <QuickMetrics />

      {/* Contextual Insights Section */}
      <section className="bg-white rounded-lg shadow-sm p-4 sm:p-6 border border-gray-200/80 animate-fadeIn" style={{ animationDelay: '200ms' }}>
        <h2 className="text-xl font-bold text-gray-800 mb-4">Smart Insights</h2>
        <div className="space-y-4">
          {insights.length > 0 ? (
            insights.map((insight) => (
              <ProgressiveInsightCard
                key={insight.id}
                insight={insight}
                isExpanded={expandedCard === insight.id}
                onToggle={handleToggleCard}
              />
            ))
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>Sedang memuat insights...</p>
              <p className="text-sm mt-2">Pastikan WebSocket server berjalan dan database terisi data.</p>
            </div>
          )}
        </div>
      </section>

      {/* Context Mode Simulator for Demo */}
      <section className="bg-white rounded-lg shadow-sm p-4 border border-gray-200/80 animate-fadeIn" style={{ animationDelay: '300ms' }}>
        <h3 className="font-semibold text-gray-800 mb-3">Demo: Simulasi Context Mode</h3>
        <div className="flex flex-wrap gap-2">
          {(Object.entries(contextConfig) as [ContextMode, { title: string }][]).map(([mode, config]) => (
            <button
              key={mode}
              onClick={() => setContextMode(mode)}
              className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                contextMode === mode
                  ? 'bg-gray-800 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {config.title}
            </button>
          ))}
        </div>
        <p className="text-xs text-gray-500 mt-3">
          * Dalam implementasi nyata, konteks berubah otomatis berdasarkan waktu.
        </p>
      </section>
    </div>
  );
};

export default DashboardPage;
