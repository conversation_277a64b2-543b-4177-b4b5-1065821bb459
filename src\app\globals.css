@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure this comes after Tailwind directives if you're overriding or adding utilities */

:root {
  --background: #ffffff;
  --foreground: #171717;
}



@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  /* font-family will be handled by <PERSON><PERSON><PERSON> font variables in layout.tsx */
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    0% { opacity: 0; transform: translateY(-10px); }
    100% { opacity: 1; transform: translateY(0); }
  }
}
