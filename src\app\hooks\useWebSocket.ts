"use client";

import { useState, useEffect, useRef } from 'react';

const WS_URL = 'ws://localhost:3000';

interface WebSocketOptions<T> {
  onMessage: (data: T) => void;
  onOpen?: () => void;
  onClose?: () => void;
  onError?: (event: Event) => void;
}

export const useWebSocket = <T,>({ onMessage, onOpen, onClose, onError }: WebSocketOptions<T>) => {
  const [isConnected, setIsConnected] = useState(false);
  const ws = useRef<WebSocket | null>(null);

  useEffect(() => {
    // Pastikan kode ini hanya berjalan di client side
    if (typeof window === 'undefined') {
      return;
    }

    const connect = () => {
      ws.current = new WebSocket(WS_URL);

      ws.current.onopen = () => {
        console.log('[WebSocket] Connected to server');
        setIsConnected(true);
        onOpen?.();
      };

      ws.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          onMessage(data);
        } catch (error) {
          console.error('[WebSocket] Error parsing message:', error);
        }
      };

      ws.current.onerror = (event) => {
        console.error('[WebSocket] Connection Error. Is the WebSocket server running? Please ensure you are using "npm run dev:ws".', event);
        onError?.(event);
      };

      ws.current.onclose = () => {
        console.log('[WebSocket] Disconnected. Attempting to reconnect...');
        setIsConnected(false);
        onClose?.();
        // Coba koneksi ulang setelah 3 detik
        setTimeout(connect, 3000);
      };
    };

    connect();

    return () => {
      if (ws.current) {
        // Hentikan rekoneksi otomatis saat komponen di-unmount
        ws.current.onclose = null; 
        ws.current.close();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Dependensi kosong memastikan ini hanya berjalan sekali saat mount

  return { isConnected };
};
