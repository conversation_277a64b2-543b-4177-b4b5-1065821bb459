"use client";

import React from 'react';
import Link from 'next/link';
import { Users, Package, Receipt, BarChart3 } from 'lucide-react';

const managementItems = [
  {
    title: 'Pelanggan',
    description: 'Kelola data pelanggan dan riwayat transaksi',
    href: '/manage/customers',
    icon: Users,
    color: 'bg-blue-500',
  },
  {
    title: 'Material',
    description: 'Manajemen inventaris dan stok material',
    href: '/manage/materials',
    icon: Package,
    color: 'bg-green-500',
  },
  {
    title: 'Transaksi',
    description: 'Kelola pesanan dan layanan laundry',
    href: '/manage/transactions',
    icon: Receipt,
    color: 'bg-purple-500',
  },
  {
    title: 'Analytics',
    description: 'Lihat laporan dan analisis bisnis',
    href: '/analytics',
    icon: BarChart3,
    color: 'bg-orange-500',
  },
];

export default function ManagePage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Area Manajemen</h1>
        <p className="text-gray-600 mt-2">
          <PERSON><PERSON>la semua aspek bisnis laundry Anda dari satu tempat
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {managementItems.map((item) => {
          const Icon = item.icon;
          return (
            <Link
              key={item.href}
              href={item.href}
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow group"
            >
              <div className="flex items-center gap-4">
                <div className={`p-3 rounded-lg ${item.color}`}>
                  <Icon size={24} className="text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {item.description}
                  </p>
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-2">
          Tips Manajemen
        </h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Periksa stok material secara berkala untuk menghindari kehabisan</li>
          <li>• Monitor transaksi yang tertunda untuk memastikan kepuasan pelanggan</li>
          <li>• Gunakan analytics untuk memahami pola bisnis dan mengoptimalkan operasi</li>
          <li>• Update data pelanggan untuk meningkatkan layanan personal</li>
        </ul>
      </div>
    </div>
  );
}
