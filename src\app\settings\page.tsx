"use client";

import React, { useState } from 'react';
import { Settings, Database, Wifi, Bell, Shield, Palette, Clock } from 'lucide-react';

interface SettingsSection {
  id: string;
  title: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  description: string;
}

const settingsSections: SettingsSection[] = [
  {
    id: 'general',
    title: 'Pengaturan Umum',
    icon: Settings,
    description: 'Konfigurasi dasar aplikasi',
  },
  {
    id: 'database',
    title: 'Database',
    icon: Database,
    description: 'Koneksi dan backup database',
  },
  {
    id: 'realtime',
    title: 'Real-time',
    icon: Wifi,
    description: 'WebSocket dan sinkronisasi data',
  },
  {
    id: 'notifications',
    title: 'Notifikasi',
    icon: Bell,
    description: 'Alert dan pemberitahuan',
  },
  {
    id: 'security',
    title: 'Keamanan',
    icon: Shield,
    description: 'Autentikasi dan otorisasi',
  },
  {
    id: 'appearance',
    title: '<PERSON><PERSON><PERSON>',
    icon: Palette,
    description: 'Theme dan UI preferences',
  },
];

const SettingsPage: React.FC = () => {
  const [activeSection, setActiveSection] = useState('general');
  const [settings, setSettings] = useState({
    general: {
      businessName: 'LaundrySense',
      timezone: 'Asia/Jakarta',
      currency: 'IDR',
      language: 'id',
    },
    database: {
      connectionStatus: 'connected',
      lastBackup: '2024-01-15T10:30:00Z',
      autoBackup: true,
      backupInterval: '24h',
    },
    realtime: {
      websocketEnabled: true,
      websocketUrl: 'ws://localhost:3001',
      updateInterval: 30,
      connectionStatus: 'connected',
    },
    notifications: {
      lowStockAlerts: true,
      overduePickups: true,
      newOrders: true,
      systemAlerts: true,
      emailNotifications: false,
    },
    security: {
      sessionTimeout: 60,
      requireAuth: false,
      apiRateLimit: 100,
      logLevel: 'info',
    },
    appearance: {
      theme: 'light',
      compactMode: false,
      showAnimations: true,
      defaultView: 'dashboard',
    },
  });

  const handleSettingChange = (section: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section as keyof typeof prev],
        [key]: value,
      },
    }));
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Nama Bisnis
        </label>
        <input
          type="text"
          value={settings.general.businessName}
          onChange={(e) => handleSettingChange('general', 'businessName', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Zona Waktu
        </label>
        <select
          value={settings.general.timezone}
          onChange={(e) => handleSettingChange('general', 'timezone', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="Asia/Jakarta">Asia/Jakarta (WIB)</option>
          <option value="Asia/Makassar">Asia/Makassar (WITA)</option>
          <option value="Asia/Jayapura">Asia/Jayapura (WIT)</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Mata Uang
        </label>
        <select
          value={settings.general.currency}
          onChange={(e) => handleSettingChange('general', 'currency', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="IDR">Indonesian Rupiah (IDR)</option>
          <option value="USD">US Dollar (USD)</option>
        </select>
      </div>
    </div>
  );

  const renderDatabaseSettings = () => (
    <div className="space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-md p-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-sm font-medium text-green-800">Database Connected</span>
        </div>
        <p className="text-sm text-green-700 mt-1">
          Koneksi ke database MySQL aktif dan stabil
        </p>
      </div>

      <div>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={settings.database.autoBackup}
            onChange={(e) => handleSettingChange('database', 'autoBackup', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm font-medium text-gray-700">Auto Backup</span>
        </label>
        <p className="text-sm text-gray-500 mt-1">
          Backup otomatis database setiap 24 jam
        </p>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Backup Terakhir
        </label>
        <p className="text-sm text-gray-600">
          {new Date(settings.database.lastBackup).toLocaleString('id-ID')}
        </p>
      </div>

      <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
        Backup Sekarang
      </button>
    </div>
  );

  const renderRealtimeSettings = () => (
    <div className="space-y-6">
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
          <span className="text-sm font-medium text-blue-800">WebSocket Connected</span>
        </div>
        <p className="text-sm text-blue-700 mt-1">
          Real-time updates aktif dan berjalan normal
        </p>
      </div>

      <div>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={settings.realtime.websocketEnabled}
            onChange={(e) => handleSettingChange('realtime', 'websocketEnabled', e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          <span className="text-sm font-medium text-gray-700">Enable WebSocket</span>
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Update Interval (detik)
        </label>
        <input
          type="number"
          value={settings.realtime.updateInterval}
          onChange={(e) => handleSettingChange('realtime', 'updateInterval', parseInt(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          min="5"
          max="300"
        />
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key}>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="text-sm font-medium text-gray-700">
              {key === 'lowStockAlerts' && 'Alert Stok Rendah'}
              {key === 'overduePickups' && 'Alert Pickup Terlambat'}
              {key === 'newOrders' && 'Notifikasi Pesanan Baru'}
              {key === 'systemAlerts' && 'Alert Sistem'}
              {key === 'emailNotifications' && 'Notifikasi Email'}
            </span>
          </label>
        </div>
      ))}
    </div>
  );

  const renderContent = () => {
    switch (activeSection) {
      case 'general':
        return renderGeneralSettings();
      case 'database':
        return renderDatabaseSettings();
      case 'realtime':
        return renderRealtimeSettings();
      case 'notifications':
        return renderNotificationSettings();
      case 'security':
        return (
          <div className="text-center py-8 text-gray-500">
            <Shield size={48} className="mx-auto mb-4 text-gray-300" />
            <p>Pengaturan keamanan akan tersedia di versi mendatang</p>
          </div>
        );
      case 'appearance':
        return (
          <div className="text-center py-8 text-gray-500">
            <Palette size={48} className="mx-auto mb-4 text-gray-300" />
            <p>Pengaturan tampilan akan tersedia di versi mendatang</p>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Pengaturan</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-2">
            {settingsSections.map((section) => {
              const Icon = section.icon;
              return (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    activeSection === section.id
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <Icon size={20} className={activeSection === section.id ? 'text-blue-600' : 'text-gray-400'} />
                    <div>
                      <div className="font-medium">{section.title}</div>
                      <div className="text-xs text-gray-500">{section.description}</div>
                    </div>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900">
                {settingsSections.find(s => s.id === activeSection)?.title}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {settingsSections.find(s => s.id === activeSection)?.description}
              </p>
            </div>

            {renderContent()}

            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="flex justify-end gap-3">
                <button className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors">
                  Reset
                </button>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                  Simpan Perubahan
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
