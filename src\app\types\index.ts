/**
 * Frontend specific types for LaundrySense Dashboard
 */

// Re-export or define types similar to src/lib/contextual-intelligence/types.ts
// For shell purposes, we'll define simplified versions or placeholders

export type TimeContext = 'morning' | 'midday' | 'evening' | 'night' | 'planning' | 'operational';
export type BusinessCycleContext = 'peak_season' | 'low_season' | 'normal_season' | 'transition';
export type UserBehaviorContext = 'normal_mode' | 'stress_mode' | 'growth_mode' | 'idle';
export type DataQualityContext = 'high_confidence' | 'medium_confidence' | 'low_confidence' | 'critical_issues';

export interface CurrentContextObject {
  time: TimeContext;
  businessCycle: BusinessCycleContext;
  userBehavior: UserBehaviorContext;
  dataQuality: DataQualityContext;
}

export type DashboardMode = 
  | 'operational_overview'
  | 'strategic_planning'
  | 'alert_mode'
  | 'maintenance_mode'
  | 'growth_analysis';

export interface Insight {
  id: string;
  priority: 'high' | 'medium' | 'low';
  title: string;
  shortDescription: string;
  // Details for progressive disclosure
  contextText?: string; // Renamed from 'context' to avoid conflict with React context keyword
  action?: string; // What can the user do?
  impact?: string; // What is the potential benefit?
  timeEstimate?: string; // How long might it take?
}

export interface DashboardContextType {
  context: DashboardState;
}

export interface DashboardState {
  currentContextObject: CurrentContextObject;
  recommendedDashboardMode: DashboardMode;
  priorityInsightsList: Insight[];
  dataQualityScore: number; // 0-100
  contextConfidence: number; // 0-1
  contextReasons: {
    time: string;
    businessCycle: string;
    userBehavior: string;
    dataQuality: string;
  };
  detectionTimestamp: Date;
}

// Initial mock data for the context
export const initialMockContext: DashboardState = {
  currentContextObject: {
    time: 'morning',
    businessCycle: 'normal_season',
    userBehavior: 'normal_mode',
    dataQuality: 'high_confidence',
  },
  recommendedDashboardMode: 'operational_overview',
  priorityInsightsList: [
    {
      id: 'insight1',
      priority: 'high',
      title: 'Target Penjualan Harian',
      shortDescription: 'Tinjau target penjualan dan progres Anda hari ini.',
      contextText: 'Pagi hari adalah waktu yang tepat untuk menetapkan fokus operasional.',
      action: 'Buka laporan penjualan, identifikasi pelanggan potensial.',
      impact: 'Meningkatkan kemungkinan pencapaian target harian.',
      timeEstimate: '15 menit'
    },
    {
      id: 'insight2',
      priority: 'medium',
      title: 'Optimasi Stok Bahan Baku',
      shortDescription: 'Periksa tingkat stok deterjen dan pewangi.',
      contextText: 'Stok optimal memastikan kelancaran operasional sepanjang hari.',
      action: 'Cek inventaris, buat pesanan jika ada yang kurang.',
      impact: 'Menghindari kehabisan bahan di jam sibuk.',
      timeEstimate: '20 menit'
    },
    {
      id: 'insight3',
      priority: 'low',
      title: 'Analisa Perilaku Pelanggan Mingguan',
      shortDescription: 'Luangkan waktu untuk melihat tren pelanggan minggu lalu.',
      contextText: 'Memahami pola pelanggan membantu strategi pemasaran.',
      action: 'Buka laporan pelanggan, identifikasi pelanggan loyal dan yang berisiko churn.',
      impact: 'Meningkatkan retensi dan personalisasi layanan.',
      timeEstimate: '30 menit'
    }
  ],
  dataQualityScore: 95,
  contextConfidence: 0.9,
  contextReasons: {
    time: 'Sekarang pagi hari, jam operasional dimulai.',
    businessCycle: 'Musim operasional normal berdasarkan data historis.',
    userBehavior: 'Aktivitas pengguna standar terdeteksi.',
    dataQuality: 'Kualitas data transaksi dan inventaris sangat baik.',
  },
  detectionTimestamp: new Date(),
};
