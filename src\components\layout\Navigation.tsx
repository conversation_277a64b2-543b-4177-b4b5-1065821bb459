"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Users, 
  Package, 
  Receipt, 
  BarChart3, 
  Settings,
  Zap 
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  description?: string;
}

const navigationItems: NavigationItem[] = [
  {
    name: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    description: 'Overview & Insights',
  },
  {
    name: '<PERSON><PERSON>ng<PERSON>',
    href: '/manage/customers',
    icon: Users,
    description: 'Manajemen Pelanggan',
  },
  {
    name: 'Material',
    href: '/manage/materials',
    icon: Package,
    description: 'Inventaris & Stok',
  },
  {
    name: 'Transaksi',
    href: '/manage/transactions',
    icon: Receipt,
    description: '<PERSON><PERSON><PERSON> & Layanan',
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    href: '/analytics',
    icon: BarChart3,
    description: '<PERSON><PERSON><PERSON> & Tren',
  },
];

interface NavigationProps {
  className?: string;
}

const Navigation: React.FC<NavigationProps> = ({ className = '' }) => {
  const pathname = usePathname();

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/dashboard' || pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <nav className={`bg-white border-r border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="bg-blue-600 p-2 rounded-lg">
            <Zap size={20} className="text-white" />
          </div>
          <div>
            <h1 className="text-lg font-bold text-gray-900">LaundrySense</h1>
            <p className="text-sm text-gray-500">Smart Laundry Management</p>
          </div>
        </div>
      </div>

      {/* Navigation Items */}
      <div className="p-4 space-y-2">
        {navigationItems.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={`
                flex items-center gap-3 px-3 py-2 rounded-lg transition-colors
                ${active 
                  ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                  : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                }
              `}
            >
              <Icon 
                size={20} 
                className={active ? 'text-blue-600' : 'text-gray-400'} 
              />
              <div className="flex-1">
                <div className="font-medium">{item.name}</div>
                {item.description && (
                  <div className="text-xs text-gray-500">{item.description}</div>
                )}
              </div>
            </Link>
          );
        })}
      </div>

      {/* Footer */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <Link
          href="/settings"
          className="flex items-center gap-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors"
        >
          <Settings size={20} className="text-gray-400" />
          <span className="font-medium">Pengaturan</span>
        </Link>
      </div>
    </nav>
  );
};

export default Navigation;
