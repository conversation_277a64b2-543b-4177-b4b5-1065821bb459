"use client";

import { useState, useCallback } from 'react';

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  details?: any;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

interface UseApiOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export const useApi = <T = any>(options: UseApiOptions = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const request = useCallback(async (
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any
  ): Promise<ApiResponse<T>> => {
    setLoading(true);
    setError(null);

    try {
      const config: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body && method !== 'GET') {
        config.body = JSON.stringify(body);
      }

      const response = await fetch(url, config);
      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      if (data.success && options.onSuccess) {
        options.onSuccess(data.data);
      }

      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      setError(errorMessage);

      if (options.onError) {
        options.onError(errorMessage);
      }

      return {
        success: false,
        error: errorMessage,
      };
    } finally {
      setLoading(false);
    }
  }, [options]);

  const get = useCallback((url: string) => request(url, 'GET'), [request]);
  const post = useCallback((url: string, body: any) => request(url, 'POST', body), [request]);
  const put = useCallback((url: string, body: any) => request(url, 'PUT', body), [request]);
  const del = useCallback((url: string) => request(url, 'DELETE'), [request]);

  return {
    loading,
    error,
    request,
    get,
    post,
    put,
    delete: del,
  };
};

// Specific hooks for different entities
export const useCustomers = () => {
  const api = useApi();

  const getCustomers = useCallback(async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    const url = `/api/customers${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return api.get(url);
  }, [api]);

  const createCustomer = useCallback((customer: any) => {
    return api.post('/api/customers', customer);
  }, [api]);

  const updateCustomer = useCallback((id: string, customer: any) => {
    return api.put(`/api/customers/${id}`, customer);
  }, [api]);

  const deleteCustomer = useCallback((id: string) => {
    return api.delete(`/api/customers/${id}`);
  }, [api]);

  return {
    ...api,
    getCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer,
  };
};

export const useMaterials = () => {
  const api = useApi();

  const getMaterials = useCallback(async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    category?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    if (params?.category) searchParams.set('category', params.category);
    if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    const url = `/api/materials${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return api.get(url);
  }, [api]);

  const createMaterial = useCallback((material: any) => {
    return api.post('/api/materials', material);
  }, [api]);

  const updateMaterial = useCallback((id: string, material: any) => {
    return api.put(`/api/materials/${id}`, material);
  }, [api]);

  const deleteMaterial = useCallback((id: string) => {
    return api.delete(`/api/materials/${id}`);
  }, [api]);

  return {
    ...api,
    getMaterials,
    createMaterial,
    updateMaterial,
    deleteMaterial,
  };
};

export const useTransactions = () => {
  const api = useApi();

  const getTransactions = useCallback(async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.set('page', params.page.toString());
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.search) searchParams.set('search', params.search);
    if (params?.status) searchParams.set('status', params.status);
    if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
    if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);

    const url = `/api/transactions${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    return api.get(url);
  }, [api]);

  const createTransaction = useCallback((transaction: any) => {
    return api.post('/api/transactions', transaction);
  }, [api]);

  const updateTransaction = useCallback((id: string, transaction: any) => {
    return api.put(`/api/transactions/${id}`, transaction);
  }, [api]);

  const deleteTransaction = useCallback((id: string) => {
    return api.delete(`/api/transactions/${id}`);
  }, [api]);

  return {
    ...api,
    getTransactions,
    createTransaction,
    updateTransaction,
    deleteTransaction,
  };
};

// Hook for getting customers list for dropdowns
export const useCustomersList = () => {
  const api = useApi();

  const getCustomersList = useCallback(async () => {
    return api.get('/api/customers?limit=1000');
  }, [api]);

  return {
    ...api,
    getCustomersList,
  };
};

// Hook for getting materials list for dropdowns
export const useMaterialsList = () => {
  const api = useApi();

  const getMaterialsList = useCallback(async () => {
    return api.get('/api/materials?limit=1000');
  }, [api]);

  return {
    ...api,
    getMaterialsList,
  };
};
