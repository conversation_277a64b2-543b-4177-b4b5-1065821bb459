# Dokumentasi Arsitektur Pustaka Inti (`src/lib`)

Dokumen ini menjelaskan arsitektur dan komponen utama yang ada di dalam direktori `src/lib` dari proyek LaundrySense. Direktori ini adalah jantung dari aplikasi, menampung semua logika bisnis, interaksi database, dan mesin kecerdasan buatan.

## Filosofi Desain

Pustaka ini dirancang berdasarkan tiga pilar utama yang bekerja sama untuk memberikan pengalaman pengguna yang cerdas dan adaptif:

1.  **Analisis Pola (Pattern Analysis)**: <PERSON>sin yang bekerja secara asinkron untuk menganalisis data historis dan mengekstrak pola bisnis jangka panjang.
2.  **Deteksi Konteks (Context Detection)**: Mekanisme yang secara *real-time* memahami situasi operasional saat ini (misalnya, waktu, perilaku pengguna, kualitas data).
3.  **Generasi <PERSON>rda<PERSON> (Smart Insight Generation)**: Otak yang menggabungkan hasil analisis pola dan deteksi konteks untuk menghasilkan rekomendasi yang relevan dan dapat ditindaklanjuti.

---

## Rincian Direktori dan File

Berikut adalah rincian dari setiap komponen utama di dalam `src/lib`.

### 📄 `prisma.ts`

-   **Tujuan**: Titik pusat untuk semua interaksi dengan database.
-   **Fungsi**: Menginisialisasi dan mengekspor klien Prisma ORM. Semua modul lain yang perlu membaca atau menulis ke database MySQL akan mengimpor klien dari file ini. Ini memastikan koneksi database dikelola secara efisien dan konsisten.

### 📁 `pattern-analysis/`

-   **Tujuan**: Menganalisis data historis untuk mengidentifikasi tren dan pola yang signifikan.
-   **Alur Kerja**: Skrip di dalam direktori ini (dijalankan oleh `cron/`) akan memproses data dari tabel `Transactions` dan lainnya untuk menghitung dan menyimpan pola ke dalam tabel `CustomerPatterns`, `MaterialUsagePatterns`, dan `RevenueTrends`.
-   **Komponen Utama**:
    -   `pattern-calculator.ts`: Skrip orkestrator utama yang memanggil berbagai penganalisis pola.
    -   `customer-patterns.ts`: Menganalisis frekuensi kunjungan, preferensi layanan, dan segmentasi pelanggan.
    -   `material-patterns.ts`: Menganalisis tingkat konsumsi bahan, memprediksi kapan stok akan habis, dan mengidentifikasi korelasi penggunaan.
    -   `revenue-patterns.ts`: Menganalisis tren pendapatan harian, mingguan, dan bulanan untuk menetapkan target dan mengidentifikasi anomali.

### 📁 `cron/`

-   **Tujuan**: Menjalankan tugas-tugas latar belakang secara terjadwal.
-   **Fungsi**: Berisi skrip-skrip yang dirancang untuk dieksekusi oleh layanan *cron-job* (atau penjadwal tugas serupa). Tugas utamanya adalah memicu `pattern-calculator.ts` secara berkala (misalnya, setiap malam) untuk memastikan data pola selalu mutakhir.

### 📁 `contextual-intelligence/`

-   **Tujuan**: Otak *real-time* dari aplikasi. Direktori ini bertanggung jawab untuk menghasilkan wawasan yang ditampilkan di dasbor.
-   **Alur Kerja**: 
    1.  `context-detector.ts` mendeteksi konteks saat ini.
    2.  Data *real-time* (misalnya, dari API) dan data pola (dari database) dikumpulkan.
    3.  `engines/smart-insight-generator.ts` mengevaluasi semua *template* wawasan yang tersedia di `templates/`.
    4.  Ia mencocokkan *template* yang relevan dengan konteks saat ini dan yang kondisi datanya terpenuhi.
    5.  Wawasan yang cocok kemudian diisi dengan data dinamis dan dikirim ke antarmuka pengguna.
-   **Komponen Utama**:
    -   `context-detector.ts`: Mendeteksi konteks seperti `time` (pagi, siang, malam), `userBehavior` (mode normal, mode stres), dan `dataQuality`.
    -   `templates/`: Direktori yang berisi semua definisi wawasan dalam format deklaratif. Ini memungkinkan penambahan wawasan baru tanpa mengubah kode logika.
    -   `types/` & `types.ts`: Mendefinisikan semua struktur data dan antarmuka TypeScript, memastikan keamanan tipe di seluruh lapisan.
    -   `engines/`: Berisi mesin-mesin pemroses utama.
        -   `smart-insight-generator.ts`: Mesin inti yang fleksibel dan berbasis *template* yang telah kita kembangkan.
        -   `progressive-disclosure.ts`: Mesin yang mengelola bagaimana informasi disajikan kepada pengguna, dari ringkasan tingkat tinggi hingga detail yang dapat ditindaklanjuti.

---

## Bagaimana Semuanya Bekerja Sama: Contoh Kasus

1.  **Malam Hari**: Skrip di `cron/` berjalan, memicu `pattern-analysis/revenue-patterns.ts`. Ia menghitung bahwa rata-rata pendapatan pada hari Selasa adalah Rp 500.000 dan menyimpannya sebagai **pola**.
2.  **Besok (Selasa Sore)**: Pengguna membuka dasbor.
3.  `context-detector.ts` mendeteksi konteks: `{ time: 'evening' }`.
4.  Aplikasi mengambil data **real-time**: pendapatan hari ini adalah Rp 600.000.
5.  `smart-insight-generator.ts` menerima konteks dan data ini. Ia memindai `templates/` dan menemukan *template* wawasan 'evening_revenue_summary_positive'.
6.  *Template* ini cocok karena `applicableContexts` adalah `evening` dan kondisinya (`realTimeData.revenue` >= `patternData.target`) terpenuhi (Rp 600.000 >= Rp 500.000).
7.  *Engine* mengisi *template* dengan data: "Kinerja Hari Ini Luar Biasa! Pendapatan mencapai Rp 600.000, melampaui target (Rp 500.000)."
8.  Wawasan cerdas ini ditampilkan kepada pengguna.
