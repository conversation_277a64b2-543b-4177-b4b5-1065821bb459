/**
 * Business Cycle Context Analyzer
 * 
 * Specialized analyzer for business cycle context detection
 */

import { 
  BusinessCycleContext, 
  BusinessCycleAnalysis, 
  ContextDetectionConfig,
  HistoricalBusinessData,
  PatternData 
} from '../types';

export class BusinessCycleAnalyzer {
  private config: ContextDetectionConfig;

  constructor(config: ContextDetectionConfig) {
    this.config = config;
  }

  /**
   * Analyze business cycle context from historical data and patterns
   */
  public analyze(
    historicalData: HistoricalBusinessData,
    patternData: PatternData
  ): BusinessCycleAnalysis {
    const currentSeasonalFactor = patternData.revenue_patterns.seasonal_factor_average;
    const recentGrowthRate = historicalData.revenue_trends.growth_rate_percentage;
    const transactionVolumeTrend = patternData.revenue_patterns.growth_trend;

    const businessCycleContext = this.determineBusinessCycle(currentSeasonalFactor);

    return {
      currentSeasonalFactor,
      recentGrowthRate,
      transactionVolumeTrend,
      businessCycleContext
    };
  }

  /**
   * Determine business cycle based on seasonal factor
   */
  private determineBusinessCycle(seasonalFactor: number): BusinessCycleContext {
    if (seasonalFactor >= this.config.seasonalFactorThresholds.peak) {
      return 'peak_season';
    } else if (seasonalFactor <= this.config.seasonalFactorThresholds.low) {
      return 'low_season';
    } else {
      return 'normal_season';
    }
  }

  /**
   * Generate explanation for business cycle context decision
   */
  public getContextReason(analysis: BusinessCycleAnalysis): string {
    const cycleMap = {
      peak_season: `High seasonal activity (factor: ${analysis.currentSeasonalFactor.toFixed(2)})`,
      normal_season: `Normal seasonal activity (factor: ${analysis.currentSeasonalFactor.toFixed(2)})`,
      low_season: `Low seasonal activity (factor: ${analysis.currentSeasonalFactor.toFixed(2)})`
    };
    return cycleMap[analysis.businessCycleContext];
  }

  /**
   * Get confidence score based on data completeness
   */
  public getConfidenceScore(dataCompleteness: number): number {
    return Math.min(1.0, dataCompleteness / 100);
  }

  /**
   * Get business cycle specific insights
   */
  public getRecommendedInsights(context: BusinessCycleContext): string[] {
    const insightMap = {
      peak_season: ['inventory_optimization', 'peak_hour_optimization'],
      normal_season: ['operational_efficiency', 'customer_satisfaction'],
      low_season: ['customer_retention_suggestion', 'cost_efficiency_review']
    };
    return insightMap[context] || [];
  }
}
