/**
 * Data Quality Context Analyzer
 * 
 * Specialized analyzer for data quality assessment
 */

import { 
  DataQualityContext, 
  DataQualityAnalysis, 
  ContextDetectionConfig,
  DataCompletenessMetrics,
  PatternData 
} from '../types';

export class DataQualityAnalyzer {
  private config: ContextDetectionConfig;

  constructor(config: ContextDetectionConfig) {
    this.config = config;
  }

  /**
   * Analyze data quality context from completeness metrics and pattern data
   */
  public analyze(
    completenessMetrics: DataCompletenessMetrics,
    patternData: PatternData
  ): DataQualityAnalysis {
    const overallCompleteness = this.calculateOverallCompleteness(completenessMetrics);
    const dataFreshness = this.calculateDataFreshness(completenessMetrics);
    const patternReliability = this.calculatePatternReliability(completenessMetrics);

    const qualityContext = this.determineQualityContext(
      overallCompleteness,
      dataFreshness,
      patternReliability
    );

    return {
      overallCompleteness,
      dataFreshness,
      patternReliability,
      qualityContext
    };
  }

  /**
   * Calculate overall data completeness percentage
   */
  private calculateOverallCompleteness(metrics: DataCompletenessMetrics): number {
    const completenessScores = [
      metrics.transactions.completeness_percentage,
      metrics.customers.completeness_percentage,
      metrics.materials.completeness_percentage
    ];
    return completenessScores.reduce((sum, score) => sum + score, 0) / completenessScores.length;
  }

  /**
   * Calculate data freshness score based on last update times
   */
  private calculateDataFreshness(metrics: DataCompletenessMetrics): number {
    const now = new Date();
    const transactionAge = now.getTime() - new Date(metrics.transactions.last_updated).getTime();
    const patternAge = now.getTime() - new Date(metrics.patterns.last_calculated).getTime();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    
    const transactionFreshness = Math.max(0, 1 - (transactionAge / maxAge));
    const patternFreshness = Math.max(0, 1 - (patternAge / (7 * maxAge))); // Patterns can be up to 7 days old
    
    return (transactionFreshness + patternFreshness) / 2 * 100;
  }

  /**
   * Calculate pattern reliability score
   */
  private calculatePatternReliability(metrics: DataCompletenessMetrics): number {
    return metrics.patterns.average_confidence_score * 100;
  }

  /**
   * Determine quality context based on combined scores
   */
  private determineQualityContext(
    completeness: number,
    freshness: number,
    reliability: number
  ): DataQualityContext {
    const qualityScore = (completeness + freshness + reliability) / 3;
    
    if (qualityScore >= this.config.dataQualityThresholds.high) {
      return 'high_confidence';
    } else if (qualityScore >= this.config.dataQualityThresholds.medium) {
      return 'medium_confidence';
    } else {
      return 'low_confidence';
    }
  }

  /**
   * Calculate overall data quality score (0-100)
   */
  public calculateQualityScore(analysis: DataQualityAnalysis): number {
    const score = (analysis.overallCompleteness + analysis.dataFreshness + analysis.patternReliability) / 3;
    return Math.round(Math.max(0, Math.min(100, score)));
  }

  /**
   * Generate explanation for data quality context decision
   */
  public getContextReason(analysis: DataQualityAnalysis): string {
    const qualityScore = (analysis.overallCompleteness + analysis.dataFreshness + analysis.patternReliability) / 3;
    return `Data quality: ${qualityScore.toFixed(1)}% (completeness: ${analysis.overallCompleteness.toFixed(1)}%, freshness: ${analysis.dataFreshness.toFixed(1)}%, reliability: ${analysis.patternReliability.toFixed(1)}%)`;
  }

  /**
   * Get confidence score based on completeness and freshness
   */
  public getConfidenceScore(analysis: DataQualityAnalysis): number {
    return (analysis.overallCompleteness + analysis.dataFreshness) / 200;
  }

  /**
   * Get data quality specific insights
   */
  public getRecommendedInsights(context: DataQualityContext): string[] {
    const insightMap = {
      high_confidence: ['advanced_analytics', 'predictive_insights'],
      medium_confidence: ['data_validation', 'pattern_verification'],
      low_confidence: ['data_quality_improvement', 'data_collection_review']
    };
    return insightMap[context] || [];
  }

  /**
   * Check if data quality requires immediate attention
   */
  public requiresImmediateAttention(analysis: DataQualityAnalysis): boolean {
    return analysis.qualityContext === 'low_confidence';
  }
}
