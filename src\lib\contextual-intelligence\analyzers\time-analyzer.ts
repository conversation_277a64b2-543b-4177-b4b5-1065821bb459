/**
 * Time Context Analyzer
 * 
 * Specialized analyzer for time-based context detection
 */

import { TimeContext, TimeAnalysis, ContextDetectionConfig } from '../types';

export class TimeAnalyzer {
  private config: ContextDetectionConfig;

  constructor(config: ContextDetectionConfig) {
    this.config = config;
  }

  /**
   * Analyze time-based context from timestamp
   */
  public analyze(timestamp: Date): TimeAnalysis {
    const hour = timestamp.getHours();
    const dayOfWeek = timestamp.getDay();
    const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
    const isBusinessHours = hour >= this.config.businessHours.start && hour < this.config.businessHours.end;

    const timeContext = this.determineTimeContext(hour, isWeekend);

    return {
      hour,
      dayOfWeek,
      isWeekend,
      isBusinessHours,
      timeContext
    };
  }

  /**
   * Determine time context based on hour and day
   */
  private determineTimeContext(hour: number, isWeekend: boolean): TimeContext {
    if (isWeekend || hour >= 22 || hour < 6) {
      return 'planning';
    } else if (hour >= 6 && hour < 11) {
      return 'morning';
    } else if (hour >= 11 && hour < 17) {
      return 'midday';
    } else {
      return 'evening';
    }
  }

  /**
   * Generate explanation for time context decision
   */
  public getContextReason(analysis: TimeAnalysis): string {
    const timeMap = {
      morning: 'Early business hours - focus on operational preparation',
      midday: 'Peak business hours - high transaction activity expected',
      evening: 'End of business day - time for completion and review',
      planning: 'Outside business hours or weekend - strategic planning time'
    };
    return timeMap[analysis.timeContext];
  }

  /**
   * Get confidence score for time analysis (always high since time is reliable)
   */
  public getConfidenceScore(): number {
    return 1.0;
  }
}
