/**
 * User Behavior Context Analyzer
 * 
 * Specialized analyzer for user behavior pattern recognition
 */

import { 
  UserBehaviorContext, 
  UserBehaviorAnalysis, 
  ContextDetectionConfig,
  UserActivity 
} from '../types';

export class UserBehaviorAnalyzer {
  private config: ContextDetectionConfig;

  constructor(config: ContextDetectionConfig) {
    this.config = config;
  }

  /**
   * Analyze user behavior context from activity log
   */
  public analyze(
    activityLog: UserActivity[],
    currentTimestamp: Date
  ): UserBehaviorAnalysis {
    const oneHourAgo = new Date(currentTimestamp.getTime() - 60 * 60 * 1000);
    const recentActivities = activityLog.filter(activity => 
      new Date(activity.timestamp) >= oneHourAgo
    );

    const recentActivityCount = recentActivities.length;
    const errorRate = this.calculateErrorRate(recentActivities);
    const analysisActionCount = this.countAnalysisActions(recentActivities);
    const bulkActionCount = this.countBulkActions(recentActivities);

    const behaviorContext = this.determineBehaviorContext(
      recentActivityCount,
      errorRate,
      analysisActionCount,
      bulkActionCount
    );

    return {
      recentActivityCount,
      errorRate,
      analysisActionCount,
      bulkActionCount,
      behaviorContext
    };
  }

  /**
   * Calculate error rate from recent activities
   */
  private calculateErrorRate(activities: UserActivity[]): number {
    if (activities.length === 0) return 0;
    
    const errorActions = activities.filter(activity => 
      activity.action.includes('error') || activity.action.includes('failed')
    );
    return errorActions.length / activities.length;
  }

  /**
   * Count analysis-related actions
   */
  private countAnalysisActions(activities: UserActivity[]): number {
    return activities.filter(activity =>
      activity.action.includes('view_report') || 
      activity.action.includes('analyze') ||
      activity.action.includes('pattern')
    ).length;
  }

  /**
   * Count bulk/batch actions
   */
  private countBulkActions(activities: UserActivity[]): number {
    return activities.filter(activity =>
      activity.action.includes('bulk') || 
      activity.action.includes('batch') ||
      activity.action.includes('export')
    ).length;
  }

  /**
   * Determine behavior context based on activity patterns
   */
  private determineBehaviorContext(
    activityCount: number,
    errorRate: number,
    analysisCount: number,
    bulkCount: number
  ): UserBehaviorContext {
    if (activityCount >= this.config.userActivityThresholds.stress_mode_actions_per_hour || errorRate > 0.2) {
      return 'stress_mode';
    } else if (analysisCount >= this.config.userActivityThresholds.growth_mode_analysis_actions || bulkCount > 0) {
      return 'growth_mode';
    } else {
      return 'normal_mode';
    }
  }

  /**
   * Generate explanation for user behavior context decision
   */
  public getContextReason(analysis: UserBehaviorAnalysis): string {
    if (analysis.behaviorContext === 'stress_mode') {
      return `High activity detected (${analysis.recentActivityCount} actions/hour, ${(analysis.errorRate * 100).toFixed(1)}% error rate)`;
    } else if (analysis.behaviorContext === 'growth_mode') {
      return `Growth-focused activity (${analysis.analysisActionCount} analysis actions, ${analysis.bulkActionCount} bulk actions)`;
    } else {
      return `Normal activity level (${analysis.recentActivityCount} actions/hour)`;
    }
  }

  /**
   * Get confidence score based on activity sample size
   */
  public getConfidenceScore(activityCount: number): number {
    return Math.min(1.0, activityCount / 10);
  }

  /**
   * Get behavior-specific insights
   */
  public getRecommendedInsights(context: UserBehaviorContext): string[] {
    const insightMap = {
      stress_mode: ['material_restock_alert', 'data_quality_improvement'],
      growth_mode: ['growth_opportunity_analysis', 'customer_behavior_analysis'],
      normal_mode: ['operational_overview', 'daily_performance']
    };
    return insightMap[context] || [];
  }
}
