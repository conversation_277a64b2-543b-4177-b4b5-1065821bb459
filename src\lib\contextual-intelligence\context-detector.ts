/**
 * LaundrySense Context Detection System
 *
 * Intelligent context detection service that analyzes various inputs
 * to determine the current operational context and recommend appropriate
 * dashboard modes and insights.
 *
 * Refactored to use modular analyzers for better maintainability.
 */

import {
  ContextDetectionInput,
  ContextDetectionResult,
  ContextDetectionConfig,
  CurrentContextObject
} from './types';

import {
  TimeAnalyzer,
  BusinessCycleAnalyzer,
  UserBehaviorAnalyzer,
  DataQualityAnalyzer
} from './analyzers';

import { InsightGenerator } from './insight-generator';

export class ContextDetector {
  private config: ContextDetectionConfig;
  private timeAnalyzer: TimeAnalyzer;
  private businessCycleAnalyzer: BusinessCycleAnalyzer;
  private userBehaviorAnalyzer: UserBehaviorAnalyzer;
  private dataQualityAnalyzer: DataQualityAnalyzer;
  private insightGenerator: InsightGenerator;

  constructor(config?: Partial<ContextDetectionConfig>) {
    this.config = {
      timeZone: 'Asia/Jakarta',
      businessHours: {
        start: 8,
        end: 18
      },
      seasonalFactorThresholds: {
        peak: 0.7,
        low: 0.3
      },
      dataQualityThresholds: {
        high: 85,
        medium: 60
      },
      userActivityThresholds: {
        stress_mode_actions_per_hour: 20,
        growth_mode_analysis_actions: 5
      },
      ...config
    };

    // Initialize analyzers
    this.timeAnalyzer = new TimeAnalyzer(this.config);
    this.businessCycleAnalyzer = new BusinessCycleAnalyzer(this.config);
    this.userBehaviorAnalyzer = new UserBehaviorAnalyzer(this.config);
    this.dataQualityAnalyzer = new DataQualityAnalyzer(this.config);
    this.insightGenerator = new InsightGenerator();
  }

  /**
   * Main context detection method
   */
  public detectContext(input: ContextDetectionInput): ContextDetectionResult {
    const detectionTimestamp = new Date();

    // Analyze different context dimensions using specialized analyzers
    const timeAnalysis = this.timeAnalyzer.analyze(input.currentTimestamp);
    const businessCycleAnalysis = this.businessCycleAnalyzer.analyze(input.historicalBusinessData, input.patternData);
    const userBehaviorAnalysis = this.userBehaviorAnalyzer.analyze(input.recentUserActivityLog, input.currentTimestamp);
    const dataQualityAnalysis = this.dataQualityAnalyzer.analyze(input.dataCompletenessMetrics, input.patternData);

    // Build current context object
    const currentContextObject: CurrentContextObject = {
      time: timeAnalysis.timeContext,
      businessCycle: businessCycleAnalysis.businessCycleContext,
      userBehavior: userBehaviorAnalysis.behaviorContext,
      dataQuality: dataQualityAnalysis.qualityContext
    };

    // Use insight generator for recommendations
    const recommendedDashboardMode = this.insightGenerator.determineDashboardMode(currentContextObject);
    const priorityInsightsList = this.insightGenerator.generatePriorityInsights(currentContextObject, input);

    // Calculate metrics using analyzers
    const dataQualityScore = this.dataQualityAnalyzer.calculateQualityScore(dataQualityAnalysis);
    const contextConfidence = this.calculateContextConfidence(
      timeAnalysis,
      businessCycleAnalysis,
      userBehaviorAnalysis,
      dataQualityAnalysis
    );

    // Generate context reasons using analyzers
    const contextReasons = {
      time: this.timeAnalyzer.getContextReason(timeAnalysis),
      businessCycle: this.businessCycleAnalyzer.getContextReason(businessCycleAnalysis),
      userBehavior: this.userBehaviorAnalyzer.getContextReason(userBehaviorAnalysis),
      dataQuality: this.dataQualityAnalyzer.getContextReason(dataQualityAnalysis)
    };

    return {
      currentContextObject,
      recommendedDashboardMode,
      priorityInsightsList,
      dataQualityScore,
      contextConfidence,
      detectionTimestamp,
      contextReasons
    };
  }

  /**
   * Calculate confidence in context detection
   */
  private calculateContextConfidence(
    timeAnalysis: any,
    businessAnalysis: any,
    behaviorAnalysis: any,
    qualityAnalysis: any
  ): number {
    // Time context is always highly confident
    const timeConfidence = this.timeAnalyzer.getConfidenceScore();

    // Business cycle confidence based on data availability
    const businessConfidence = this.businessCycleAnalyzer.getConfidenceScore(qualityAnalysis.overallCompleteness);

    // User behavior confidence based on activity sample size
    const behaviorConfidence = this.userBehaviorAnalyzer.getConfidenceScore(behaviorAnalysis.recentActivityCount);

    // Data quality confidence based on completeness and freshness
    const qualityConfidence = this.dataQualityAnalyzer.getConfidenceScore(qualityAnalysis);

    const overallConfidence = (timeConfidence + businessConfidence + behaviorConfidence + qualityConfidence) / 4;
    return Math.round(overallConfidence * 100) / 100;
  }
}
