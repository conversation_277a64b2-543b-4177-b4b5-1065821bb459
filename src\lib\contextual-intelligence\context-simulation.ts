import { ContextDetector } from './context-detector';
import { ContextDetectionInput, ContextDetectionResult as BackendResult, UserActivity } from './types';
import { DashboardState as FrontendResult } from '@/app/types';
import { mapInsights } from './insight-mapper';

// Helper function to get a random number in a range
const getRandomNumber = (min: number, max: number) => Math.random() * (max - min) + min;

/**
 * Generates a realistic, random ContextDetectionInput for simulation.
 */
const generateRandomInput = (): ContextDetectionInput => {
  const now = new Date();
  const simulatedTimestamp = new Date(now.getTime() + Math.random() * 24 * 60 * 60 * 1000);

  // Generate some random user activities
  const userActivities: UserActivity[] = Array.from({ length: Math.floor(getRandomNumber(10, 50)) }, (_, i) => ({
    timestamp: new Date(simulatedTimestamp.getTime() - i * 60000 * Math.random()).toISOString(),
    action: getRandomNumber(0, 1) > 0.8 ? 'view_report' : 'create_transaction',
    user_id: 'user_sim_1',
  }));

  return {
    currentTimestamp: simulatedTimestamp,
    historicalBusinessData: {
      monthly_transactions: [], // Simplified for simulation
      material_usage_averages: [],
      customer_metrics: { total_customers: 100, active_customers_last_30_days: 50, average_transaction_value: 75000, customer_retention_rate: 0.6 },
      revenue_trends: { daily_average: 1500000, weekly_average: 10000000, monthly_average: 40000000, growth_rate_percentage: getRandomNumber(-5, 15) }
    },
    patternData: {
      customer_patterns: { total_analyzed: 50, high_confidence_count: 20, average_loyalty_score: 0.7, average_frequency_score: 0.5, seasonal_variance_average: 0.2 },
      material_patterns: { total_analyzed: 30, reorder_recommendations_count: Math.floor(getRandomNumber(0, 3)), average_efficiency_score: 0.8, stock_alerts_count: Math.floor(getRandomNumber(0, 5)) },
      revenue_patterns: { peak_days_count: 2, growth_trend: 'increasing', seasonal_factor_average: getRandomNumber(0.2, 1.8), revenue_volatility: 0.3 }
    },
    recentUserActivityLog: userActivities,
    dataCompletenessMetrics: {
      transactions: { last_updated: now.toISOString(), completeness_percentage: getRandomNumber(50, 100), total_records: 1000, missing_fields_count: 10 },
      customers: { last_updated: now.toISOString(), completeness_percentage: getRandomNumber(60, 100), total_records: 100, missing_fields_count: 5 },
      materials: { last_updated: now.toISOString(), completeness_percentage: getRandomNumber(70, 100), total_records: 50, missing_fields_count: 2 },
      patterns: { last_calculated: now.toISOString(), customer_patterns_count: 50, material_patterns_count: 30, revenue_trends_count: 5, average_confidence_score: 0.85 }
    },
  };
};

/**
 * Simulates a full context detection and insight generation cycle.
 */
export const generateRandomContextUpdate = (): { contextResult: FrontendResult } => {
  const contextDetector = new ContextDetector();
  const randomInput = generateRandomInput();
  const detectionResult: BackendResult = contextDetector.detectContext(randomInput);

  // Map backend string-based insights to frontend rich insight objects
  const richInsights = mapInsights(detectionResult.priorityInsightsList);

  const frontendResult: FrontendResult = {
    ...detectionResult,
    priorityInsightsList: richInsights,
  };

  return {
    contextResult: frontendResult,
  };
};


