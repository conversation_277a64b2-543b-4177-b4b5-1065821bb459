/**
 * Progressive Disclosure Engine
 * 
 * Manages layered information revelation for insights,
 * providing progressive disclosure based on user interaction
 */

import {
  GeneratedInsight,
  ProgressiveInsight,
  InsightLayer
} from '../types/insight-types';

export class ProgressiveDisclosureEngine {
  /**
   * Convert a generated insight into progressive layers
   */
  public createProgressiveInsight(insight: GeneratedInsight): ProgressiveInsight {
    const layers = this.buildInsightLayers(insight);
    
    return {
      id: insight.id,
      layers,
      currentLevel: 0, // Start with headline only
      maxLevel: layers.length - 1,
      userInteractions: {
        views: 0,
        expansions: 0,
        actions_taken: 0,
        last_viewed: new Date()
      }
    };
  }

  /**
   * Build layered structure for progressive disclosure
   */
  private buildInsightLayers(insight: GeneratedInsight): InsightLayer[] {
    const layers: InsightLayer[] = [
      // Level 0: Headline (always visible)
      {
        level: 'headline',
        content: this.createHeadline(insight),
        isVisible: true
      },
      
      // Level 1: Context (revealed on first interaction)
      {
        level: 'context',
        content: insight.context,
        data: {
          priority: insight.priority,
          category: insight.category,
          confidence: insight.confidence
        },
        isVisible: false
      },
      
      // Level 2: Action (revealed on second interaction)
      {
        level: 'action',
        content: insight.action,
        data: {
          timeEstimate: insight.timeEstimate,
          tags: insight.tags
        },
        isVisible: false
      },
      
      // Level 3: Impact (revealed on third interaction)
      {
        level: 'impact',
        content: insight.impact,
        data: {
          expectedOutcome: this.extractExpectedOutcome(insight.impact),
          businessValue: this.calculateBusinessValue(insight)
        },
        isVisible: false
      },
      
      // Level 4: Details (revealed on fourth interaction)
      {
        level: 'details',
        content: this.createDetailedAnalysis(insight),
        data: insight.relatedData,
        isVisible: false
      }
    ];

    return layers;
  }

  /**
   * Create compelling headline from insight
   */
  private createHeadline(insight: GeneratedInsight): string {
    const priorityEmoji = {
      high: '🔥',
      medium: '⚡',
      low: '💡'
    };

    const categoryEmoji = {
      revenue_target: '💰',
      inventory_management: '📦',
      customer_service: '👥',
      operational_efficiency: '⚙️',
      data_quality: '📊',
      growth_opportunity: '📈',
      cost_optimization: '💸',
      staff_management: '👨‍💼'
    };

    return `${priorityEmoji[insight.priority]} ${categoryEmoji[insight.category]} ${insight.title}`;
  }

  /**
   * Extract expected outcome from impact text
   */
  private extractExpectedOutcome(impact: string): string {
    // Extract percentage or specific outcomes from impact text
    const percentageMatch = impact.match(/(\d+)%/);
    const timeMatch = impact.match(/(\d+)\s*(hari|jam|menit|bulan)/);
    
    if (percentageMatch) {
      return `Peningkatan ${percentageMatch[1]}%`;
    } else if (timeMatch) {
      return `Hasil dalam ${timeMatch[0]}`;
    } else {
      return 'Peningkatan performa operasional';
    }
  }

  /**
   * Calculate business value score
   */
  private calculateBusinessValue(insight: GeneratedInsight): number {
    const priorityValue = {
      high: 0.8,
      medium: 0.6,
      low: 0.4
    };

    const categoryValue = {
      revenue_target: 0.9,
      inventory_management: 0.7,
      customer_service: 0.8,
      operational_efficiency: 0.7,
      data_quality: 0.6,
      growth_opportunity: 0.9,
      cost_optimization: 0.8,
      staff_management: 0.6
    };

    return (priorityValue[insight.priority] + categoryValue[insight.category]) / 2;
  }

  /**
   * Create detailed analysis for final layer
   */
  private createDetailedAnalysis(insight: GeneratedInsight): string {
    const analysis = [
      `**Analisis Mendalam:**`,
      `Insight ini dihasilkan berdasarkan analisis pola data historis dan kondisi real-time saat ini.`,
      ``,
      `**Tingkat Kepercayaan:** ${Math.round(insight.confidence * 100)}%`,
      `**Kategori:** ${this.getCategoryDescription(insight.category)}`,
      `**Estimasi Waktu:** ${insight.timeEstimate}`,
      ``,
      `**Rekomendasi Tindak Lanjut:**`,
      `1. Lakukan tindakan yang disarankan`,
      `2. Monitor hasil dalam 24 jam`,
      `3. Evaluasi dampak terhadap KPI utama`,
      `4. Dokumentasikan pembelajaran untuk optimasi future`
    ];

    return analysis.join('\n');
  }

  /**
   * Get category description
   */
  private getCategoryDescription(category: string): string {
    const descriptions = {
      revenue_target: 'Optimasi Pendapatan',
      inventory_management: 'Manajemen Stok & Inventori',
      customer_service: 'Peningkatan Layanan Pelanggan',
      operational_efficiency: 'Efisiensi Operasional',
      data_quality: 'Kualitas & Integritas Data',
      growth_opportunity: 'Peluang Pertumbuhan Bisnis',
      cost_optimization: 'Optimasi Biaya Operasional',
      staff_management: 'Manajemen Sumber Daya Manusia'
    };

    return descriptions[category] || category;
  }

  /**
   * Reveal next layer of insight
   */
  public revealNextLayer(progressiveInsight: ProgressiveInsight): ProgressiveInsight {
    if (progressiveInsight.currentLevel < progressiveInsight.maxLevel) {
      const nextLevel = progressiveInsight.currentLevel + 1;
      
      // Make next layer visible
      progressiveInsight.layers[nextLevel].isVisible = true;
      progressiveInsight.currentLevel = nextLevel;
      
      // Update interaction stats
      progressiveInsight.userInteractions.expansions++;
      progressiveInsight.userInteractions.last_viewed = new Date();
    }

    return progressiveInsight;
  }

  /**
   * Record user interaction
   */
  public recordInteraction(
    progressiveInsight: ProgressiveInsight,
    interactionType: 'view' | 'expand' | 'action'
  ): ProgressiveInsight {
    switch (interactionType) {
      case 'view':
        progressiveInsight.userInteractions.views++;
        break;
      case 'expand':
        progressiveInsight.userInteractions.expansions++;
        break;
      case 'action':
        progressiveInsight.userInteractions.actions_taken++;
        break;
    }

    progressiveInsight.userInteractions.last_viewed = new Date();
    return progressiveInsight;
  }

  /**
   * Get visible content for current level
   */
  public getVisibleContent(progressiveInsight: ProgressiveInsight): InsightLayer[] {
    return progressiveInsight.layers.filter(layer => layer.isVisible);
  }

  /**
   * Check if more content is available
   */
  public hasMoreContent(progressiveInsight: ProgressiveInsight): boolean {
    return progressiveInsight.currentLevel < progressiveInsight.maxLevel;
  }

  /**
   * Get engagement score for insight
   */
  public getEngagementScore(progressiveInsight: ProgressiveInsight): number {
    const interactions = progressiveInsight.userInteractions;
    const maxPossibleScore = 10; // views(3) + expansions(4) + actions(3)
    
    const viewScore = Math.min(interactions.views, 3);
    const expansionScore = Math.min(interactions.expansions, 4);
    const actionScore = Math.min(interactions.actions_taken, 3);
    
    return (viewScore + expansionScore + actionScore) / maxPossibleScore;
  }

  /**
   * Generate summary for analytics
   */
  public generateInteractionSummary(progressiveInsights: ProgressiveInsight[]): {
    totalInsights: number;
    averageEngagement: number;
    mostEngagedCategories: string[];
    completionRate: number;
  } {
    const totalInsights = progressiveInsights.length;
    const totalEngagement = progressiveInsights.reduce(
      (sum, insight) => sum + this.getEngagementScore(insight), 0
    );
    const averageEngagement = totalInsights > 0 ? totalEngagement / totalInsights : 0;
    
    const fullyExplored = progressiveInsights.filter(
      insight => insight.currentLevel === insight.maxLevel
    ).length;
    const completionRate = totalInsights > 0 ? fullyExplored / totalInsights : 0;

    // Extract categories from insight IDs (simplified)
    const categoryEngagement = new Map<string, number>();
    progressiveInsights.forEach(insight => {
      const category = insight.id.split('_')[0]; // Extract category from ID
      const engagement = this.getEngagementScore(insight);
      categoryEngagement.set(category, (categoryEngagement.get(category) || 0) + engagement);
    });

    const mostEngagedCategories = Array.from(categoryEngagement.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 3)
      .map(entry => entry[0]);

    return {
      totalInsights,
      averageEngagement,
      mostEngagedCategories,
      completionRate
    };
  }
}
