/**
 * Smart Insight Generator
 *
 * Advanced insight generation engine with contextual templates,
 * progressive disclosure, and intelligent prioritization
 */

import {
  InsightGenerationInput,
  InsightGenerationResult,
  GeneratedInsight,
  InsightTemplate,
  TemplateMatchResult,
  InsightGeneratorConfig,
  InsightMetrics,
  TemplateVariable
} from '../types/insight-types';

import { INSIGHT_TEMPLATES, PRIORITY_WEIGHTS } from '../templates/insight-templates';

export class SmartInsightGenerator {
  private config: InsightGeneratorConfig;
  private templates: InsightTemplate[];

  constructor(config?: Partial<InsightGeneratorConfig>) {
    this.config = {
      maxInsightsPerGeneration: 5,
      priorityWeights: {
        high: 3,
        medium: 2,
        low: 1
      },
      contextualFactors: {
        timeWeight: 0.3,
        businessCycleWeight: 0.25,
        userBehaviorWeight: 0.25,
        dataQualityWeight: 0.2
      },
      thresholds: {
        lowStockDays: 3,
        highRevenueVariance: 0.2,
        customerRetentionThreshold: 0.8,
        dataQualityMinimum: 0.6
      },
      formatting: {
        currency: 'IDR',
        locale: 'id-ID',
        timezone: 'Asia/Jakarta'
      },
      ...config
    };

    this.templates = INSIGHT_TEMPLATES;
  }

  /**
   * Generate contextual insights based on current state
   */
  public generateInsights(input: InsightGenerationInput): InsightGenerationResult {
    const generationTimestamp = new Date();

    // Find matching templates
    const matchedTemplates = this.findMatchingTemplates(input);

    // Generate insights from templates
    const insights = this.generateInsightsFromTemplates(matchedTemplates, input);

    // Sort by priority and relevance
    const sortedInsights = this.prioritizeInsights(insights);

    // Limit to max insights
    const finalInsights = sortedInsights.slice(0, this.config.maxInsightsPerGeneration);

    // Calculate statistics
    const stats = this.calculateInsightStats(finalInsights);

    return {
      insights: finalInsights,
      totalGenerated: finalInsights.length,
      highPriorityCount: stats.highPriorityCount,
      mediumPriorityCount: stats.mediumPriorityCount,
      lowPriorityCount: stats.lowPriorityCount,
      generationTimestamp,
      contextSummary: {
        primaryContext: this.getPrimaryContext(input.contextObject),
        secondaryFactors: this.getSecondaryFactors(input.contextObject),
        dataQuality: input.contextObject.dataQuality
      }
    };
  }

  /**
   * Find templates that match current context
   */
  private findMatchingTemplates(input: InsightGenerationInput): TemplateMatchResult[] {
    const matches: TemplateMatchResult[] = [];

    for (const template of this.templates) {
      const matchResult = this.evaluateTemplateMatch(template, input);
      if (matchResult.matchScore > 0) {
        matches.push(matchResult);
      }
    }

    return matches.sort((a, b) => b.matchScore - a.matchScore);
  }

  /**
   * Evaluate if a template matches current context
   */
  private evaluateTemplateMatch(
    template: InsightTemplate,
    input: InsightGenerationInput
  ): TemplateMatchResult {
    let matchScore = 0;
    const missingData: string[] = [];
    const variableValues: Record<string, any> = {};

    // Check context applicability
    const contextMatch = this.checkContextMatch(template, input.contextObject);
    matchScore += contextMatch * 0.4;

    // Check data availability
    const dataMatch = this.checkDataAvailability(template, input);
    matchScore += dataMatch.score * 0.3;
    missingData.push(...dataMatch.missing);

    // Check conditions
    const conditionMatch = this.checkConditions(template, input);
    matchScore += conditionMatch * 0.3;

    // Extract variable values
    this.extractVariableValues(template, input, variableValues);

    return {
      template,
      matchScore,
      missingData,
      variableValues
    };
  }

  /**
   * Check if template context matches current context
   */
  private checkContextMatch(template: InsightTemplate, contextObject: any): number {
    let score = 0;
    let totalChecks = 0;

    const contexts = template.applicableContexts;

    if (contexts.time) {
      totalChecks++;
      if (contexts.time.includes(contextObject.time)) score++;
    }

    if (contexts.businessCycle) {
      totalChecks++;
      if (contexts.businessCycle.includes(contextObject.businessCycle)) score++;
    }

    if (contexts.userBehavior) {
      totalChecks++;
      if (contexts.userBehavior.includes(contextObject.userBehavior)) score++;
    }

    if (contexts.dataQuality) {
      totalChecks++;
      if (contexts.dataQuality.includes(contextObject.dataQuality)) score++;
    }

    return totalChecks > 0 ? score / totalChecks : 0;
  }

  /**
   * Check data availability for template
   */
  private checkDataAvailability(
    template: InsightTemplate,
    input: InsightGenerationInput
  ): { score: number; missing: string[] } {
    const missing: string[] = [];
    let available = 0;
    let total = 0;

    if (template.requiredData.patternData) {
      for (const path of template.requiredData.patternData) {
        total++;
        if (this.getNestedValue(input.patternData, path) !== undefined) {
          available++;
        } else {
          missing.push(`patternData.${path}`);
        }
      }
    }

    if (template.requiredData.realTimeData) {
      for (const path of template.requiredData.realTimeData) {
        total++;
        if (this.getNestedValue(input.realTimeData, path) !== undefined) {
          available++;
        } else {
          missing.push(`realTimeData.${path}`);
        }
      }
    }

    return {
      score: total > 0 ? available / total : 1,
      missing
    };
  }

  /**
   * Check template conditions
   */
  private checkConditions(template: InsightTemplate, input: InsightGenerationInput): number {
    if (!template.conditions || template.conditions.length === 0) return 1;

    let passedConditions = 0;

    for (const condition of template.conditions) {
      const value = this.resolveConditionValue(condition.field, input);
      const targetValue = this.resolveConditionValue(condition.value, input);

      if (this.evaluateCondition(value, condition.operator, targetValue)) {
        passedConditions++;
      }
    }

    return passedConditions / template.conditions.length;
  }

  /**
   * Resolve condition value from input data
   */
  private resolveConditionValue(field: string, input: InsightGenerationInput): any {
    if (typeof field !== 'string') return field;

    if (field.startsWith('contextObject.')) {
      return this.getNestedValue(input.contextObject, field.substring(14));
    } else if (field.startsWith('patternData.')) {
      return this.getNestedValue(input.patternData, field.substring(12));
    } else if (field.startsWith('realTimeData.')) {
      return this.getNestedValue(input.realTimeData, field.substring(13));
    }

    return field;
  }

  /**
   * Evaluate condition based on operator
   */
  private evaluateCondition(value: any, operator: string, target: any): boolean {
    switch (operator) {
      case 'gt': return value > target;
      case 'lt': return value < target;
      case 'eq': return value === target;
      case 'gte': return value >= target;
      case 'lte': return value <= target;
      case 'contains': return String(value).includes(String(target));
      case 'exists': return value !== undefined && value !== null;
      default: return false;
    }
  }

  /**
   * Extract variable values for template
   */
  private extractVariableValues(
    template: InsightTemplate,
    input: InsightGenerationInput,
    variables: Record<string, any> // This object will be populated by this function
  ): void {
    // Process the 'variables' block from the template definition
    if (template.variables) {
      for (const placeholderName in template.variables) {
        if (Object.prototype.hasOwnProperty.call(template.variables, placeholderName)) {
          const dataPath = template.variables[placeholderName];
          const value = this.getNestedValue(input, dataPath);
          
          // Store the resolved value in the variables object, keyed by the placeholder name
          // If value is undefined, it will be handled by interpolateTemplate later (keeps placeholder)
          variables[placeholderName] = value;
        }
      }
    }

    // Note: Any pre-existing logic for 'derived' variables specific to certain templates
    // that are NOT covered by the general 'template.variables' mapping would need to be
    // re-evaluated. For now, this new approach prioritizes the declarative 'variables' mapping.
    // If a template like 'daily_revenue_target' previously relied on hardcoded extraction
    // for 'daily_target' or 'current_revenue' to then derive 'revenue_percentage' and 'performance_status',
    // those base variables ('daily_target', 'current_revenue') should now also be defined in its 'template.variables' block.

    // Example of how derived variables could be handled if needed, post-mapping:
    // if (template.id === 'daily_revenue_target') {
    //   if (variables.daily_target !== undefined && variables.current_revenue !== undefined && variables.daily_target > 0) {
    //     variables.revenue_percentage = Math.round((variables.current_revenue / variables.daily_target) * 100);
    //     variables.performance_status = variables.revenue_percentage >= 100 ? 'lebih baik' : 'di bawah';
    //   } else {
    //     variables.revenue_percentage = 0;
    //     variables.performance_status = 'data tidak lengkap';
    //   }
    // }
  }

  /**
   * Get nested value from object using dot notation, array indexing, and .length
   */
  private getNestedValue(obj: any, path: string): any {
    // Regex to split path by '.' or capture array accessors like '[index]' or '.length'
    // Handles: obj.prop, obj.arr[0], obj.arr.length, obj.arr[0].prop
    const segments = path.match(/([^[.\s]+)|(\[\d+\])|(\.length)/g);

    if (!segments) {
      // If path is empty or invalid, return undefined or the object itself if path was empty string
      return path === '' ? obj : undefined;
    }

    let current = obj;

    for (const segment of segments) {
      if (current === undefined || current === null) {
        return undefined;
      }

      if (segment.startsWith('[')) { // Array access: e.g., '[0]'
        const index = parseInt(segment.substring(1, segment.length - 1));
        if (Array.isArray(current) && !isNaN(index) && index >= 0 && index < current.length) {
          current = current[index];
        } else {
          return undefined; // Index out of bounds, not an array, or NaN index
        }
      } else if (segment === '.length') { // Length property e.g., '.length'
        if (Array.isArray(current) || typeof current === 'string') {
          current = current.length;
        } else if (typeof current === 'object' && 'length' in current) {
          current = current.length; // For other objects that might have a length property
        } else {
          return undefined; // '.length' on an unsupported type
        }
      } else { // Property access: e.g., 'propertyName'
        // Remove leading dot if segment came from .length or similar constructs that were not property names
        const key = segment.startsWith('.') ? segment.substring(1) : segment;
        if (typeof current === 'object' && key in current) {
          current = current[key];
        } else {
          return undefined; // Property not found
        }
      }
    }
    return current;
  }

  /**
   * Generate insights from matched templates
   */
  private generateInsightsFromTemplates(
    matches: TemplateMatchResult[],
    input: InsightGenerationInput
  ): GeneratedInsight[] {
    const insights: GeneratedInsight[] = [];

    for (const match of matches) {
      if (match.missingData.length === 0) {
        const insight = this.createInsightFromTemplate(match, input);
        if (insight) {
          insights.push(insight);
        }
      }
    }

    return insights;
  }

  /**
   * Create insight from template and data
   */
  private createInsightFromTemplate(
    match: TemplateMatchResult,
    input: InsightGenerationInput
  ): GeneratedInsight | null {
    try {
      const template = match.template;
      const variables = match.variableValues;

      return {
        id: `${template.id}_${Date.now()}`,
        priority: template.priority,
        category: template.category,
        title: this.interpolateTemplate(template.title, variables),
        context: this.interpolateTemplate(template.contextTemplate, variables),
        action: this.interpolateTemplate(template.actionTemplate, variables),
        impact: this.interpolateTemplate(template.impactTemplate, variables),
        timeEstimate: template.timeEstimate,
        relatedData: variables,
        confidence: match.matchScore,
        tags: template.tags
      };
    } catch (error) {
      console.error('Error creating insight from template:', error);
      return null;
    }
  }

  /**
   * Interpolate template with variables
   */
  private interpolateTemplate(template: string, variables: Record<string, any>): string {
    return template.replace(/\{(\w+)\}/g, (match, key) => {
      const value = variables[key];
      if (value === undefined) return match;

      // Format numbers as currency if they look like money
      if (typeof value === 'number' && key.includes('revenue') || key.includes('target')) {
        return this.formatCurrency(value);
      }

      return String(value);
    });
  }

  /**
   * Format number as currency
   */
  private formatCurrency(amount: number): string {
    // Simple Indonesian Rupiah formatting
    return `Rp${amount.toLocaleString('id-ID')}`;
  }

  /**
   * Prioritize insights by relevance and importance
   */
  private prioritizeInsights(insights: GeneratedInsight[]): GeneratedInsight[] {
    return insights.sort((a, b) => {
      const scoreA = this.calculateInsightScore(a);
      const scoreB = this.calculateInsightScore(b);
      return scoreB - scoreA;
    });
  }

  /**
   * Calculate insight relevance score
   */
  private calculateInsightScore(insight: GeneratedInsight): number {
    const priorityScore = PRIORITY_WEIGHTS[insight.priority];
    const confidenceScore = insight.confidence;

    return priorityScore * 0.6 + confidenceScore * 0.4;
  }

  /**
   * Calculate insight statistics
   */
  private calculateInsightStats(insights: GeneratedInsight[]) {
    return {
      highPriorityCount: insights.filter(i => i.priority === 'high').length,
      mediumPriorityCount: insights.filter(i => i.priority === 'medium').length,
      lowPriorityCount: insights.filter(i => i.priority === 'low').length
    };
  }

  /**
   * Get primary context description
   */
  private getPrimaryContext(contextObject: any): string {
    return `${contextObject.time}_${contextObject.businessCycle}_${contextObject.userBehavior}`;
  }

  /**
   * Get secondary context factors
   */
  private getSecondaryFactors(contextObject: any): string[] {
    return [
      `Data Quality: ${contextObject.dataQuality}`,
      `Time Context: ${contextObject.time}`,
      `Business Cycle: ${contextObject.businessCycle}`,
      `User Behavior: ${contextObject.userBehavior}`
    ];
  }
}
