/**
 * Insight Generator
 * 
 * Generates priority insights based on current context
 */

import {
  CurrentContextObject,
  PriorityInsight,
  ContextDetectionInput,
  DashboardMode
} from './types';

export class InsightGenerator {
  /**
   * Generate priority insights based on context
   */
  public generatePriorityInsights(
    context: CurrentContextObject,
    input: ContextDetectionInput
  ): PriorityInsight[] {
    const insights: PriorityInsight[] = [];

    // Data quality issues take highest priority
    if (context.dataQuality === 'low_confidence') {
      insights.push('data_quality_improvement');
    }

    // Add time-based insights
    insights.push(...this.getTimeBasedInsights(context, input));

    // Add business cycle insights
    insights.push(...this.getBusinessCycleInsights(context));

    // Add user behavior insights
    insights.push(...this.getUserBehaviorInsights(context));

    // Remove duplicates and limit to top 5
    return [...new Set(insights)].slice(0, 5);
  }

  /**
   * Determine recommended dashboard mode based on context
   */
  public determineDashboardMode(context: CurrentContextObject): DashboardMode {
    // Priority order: data quality issues, stress mode, then normal flow
    if (context.dataQuality === 'low_confidence') {
      return 'maintenance_mode';
    }

    if (context.userBehavior === 'stress_mode') {
      return 'alert_mode';
    }

    if (context.userBehavior === 'growth_mode') {
      return 'growth_analysis';
    }

    if (context.time === 'planning') {
      return 'strategic_planning';
    }

    return 'operational_overview';
  }

  /**
   * Get time-based insights
   */
  private getTimeBasedInsights(
    context: CurrentContextObject,
    input: ContextDetectionInput
  ): PriorityInsight[] {
    const insights: PriorityInsight[] = [];

    switch (context.time) {
      case 'morning':
        insights.push('daily_sales_target');
        if (input.patternData.material_patterns.stock_alerts_count > 0) {
          insights.push('material_restock_alert');
        }
        break;
      case 'midday':
        insights.push('peak_hour_optimization');
        insights.push('inventory_optimization');
        break;
      case 'evening':
        insights.push('customer_behavior_analysis');
        insights.push('cost_efficiency_review');
        break;
      case 'planning':
        insights.push('growth_opportunity_analysis');
        insights.push('seasonal_preparation');
        break;
    }

    return insights;
  }

  /**
   * Get business cycle insights
   */
  private getBusinessCycleInsights(context: CurrentContextObject): PriorityInsight[] {
    const insights: PriorityInsight[] = [];

    switch (context.businessCycle) {
      case 'peak_season':
        insights.push('inventory_optimization');
        insights.push('peak_hour_optimization');
        break;
      case 'low_season':
        insights.push('customer_retention_suggestion');
        insights.push('cost_efficiency_review');
        break;
      case 'normal_season':
        // Normal season gets balanced insights
        break;
    }

    return insights;
  }

  /**
   * Get user behavior insights
   */
  private getUserBehaviorInsights(context: CurrentContextObject): PriorityInsight[] {
    const insights: PriorityInsight[] = [];

    switch (context.userBehavior) {
      case 'stress_mode':
        insights.push('material_restock_alert');
        insights.push('data_quality_improvement');
        break;
      case 'growth_mode':
        insights.push('growth_opportunity_analysis');
        insights.push('customer_behavior_analysis');
        break;
      case 'normal_mode':
        // Normal mode gets standard operational insights
        break;
    }

    return insights;
  }
}
