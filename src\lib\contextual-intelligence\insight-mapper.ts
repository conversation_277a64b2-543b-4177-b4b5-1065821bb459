/**
 * Insight Mapper
 * 
 * Maps string-based PriorityInsight identifiers from the backend logic to the rich
 * Insight objects required by the frontend components.
 */

import { Insight, initialMockContext } from '@/app/types';
import { PriorityInsight } from './types';

// Create a lookup map from the detailed mock insights for efficient access.
// The key is the string identifier (e.g., 'daily_sales_target'), and the value is the full Insight object.
const insightDetailsMap = new Map<string, Omit<Insight, 'id' | 'priority'>>();

// A mapping from the backend's PriorityInsight string to the frontend's priority level.
const priorityMap: Record<PriorityInsight, 'high' | 'medium' | 'low'> = {
  // High Priority
  'data_quality_improvement': 'high',
  'material_restock_alert': 'high',
  'peak_hour_optimization': 'high',

  // Medium Priority
  'customer_retention_suggestion': 'medium',
  'inventory_optimization': 'medium',
  'daily_sales_target': 'medium',
  'cost_efficiency_review': 'medium',

  // Low Priority
  'seasonal_preparation': 'low',
  'growth_opportunity_analysis': 'low',
  'customer_behavior_analysis': 'low',
};

// Populate the map using the mock data from the frontend types.
// This ensures consistency between what the backend can generate and what the frontend can display.
initialMockContext.priorityInsightsList.forEach(mockInsight => {
  // A simple way to link backend string to frontend mock is by converting title to a key
  const key = mockInsight.title.toLowerCase().replace(/\s+/g, '_').replace('penjualan', 'sales').replace('harian', 'daily');
  
  // A more robust approach would be to have explicit IDs, but this works for the mock data.
  let id: PriorityInsight | null = null;
  if (key.includes('sales_target')) id = 'daily_sales_target';
  if (key.includes('optimasi_stok')) id = 'inventory_optimization'; // Example mapping
  if (key.includes('analisa_perilaku')) id = 'customer_behavior_analysis';

  if (id) {
    const { id: mockId, priority, ...rest } = mockInsight;
    insightDetailsMap.set(id, rest);
  }
});

// Fallback details for insights not in the initial mock
const fallbackDetails: Record<PriorityInsight, Omit<Insight, 'id' | 'priority'>> = {
    'daily_sales_target': { title: 'Target Penjualan Harian', shortDescription: 'Tinjau progres target penjualan hari ini.' },
    'material_restock_alert': { title: 'Peringatan Stok Material', shortDescription: 'Beberapa material penting hampir habis. Segera pesan ulang.' },
    'customer_retention_suggestion': { title: 'Saran Retensi Pelanggan', shortDescription: 'Identifikasi pelanggan yang berisiko churn dan tawarkan promo.' },
    'peak_hour_optimization': { title: 'Optimasi Jam Sibuk', shortDescription: 'Alokasikan sumber daya untuk menangani lonjakan permintaan.' },
    'seasonal_preparation': { title: 'Persiapan Musim Ramai', shortDescription: 'Siapkan strategi dan stok untuk menghadapi musim ramai yang akan datang.' },
    'cost_efficiency_review': { title: 'Tinjauan Efisiensi Biaya', shortDescription: 'Analisis pengeluaran dan temukan area untuk penghematan.' },
    'growth_opportunity_analysis': { title: 'Analisis Peluang Pertumbuhan', shortDescription: 'Jelajahi layanan atau pasar baru yang potensial.' },
    'data_quality_improvement': { title: 'Tingkatkan Kualitas Data', shortDescription: 'Lengkapi data yang hilang untuk mendapatkan insight yang lebih akurat.' },
    'inventory_optimization': { title: 'Optimasi Inventaris', shortDescription: 'Pastikan level stok sesuai dengan permintaan untuk efisiensi.' },
    'customer_behavior_analysis': { title: 'Analisis Perilaku Pelanggan', shortDescription: 'Pahami tren pelanggan untuk personalisasi layanan.' },
};

/**
 * Maps an array of PriorityInsight strings to an array of full Insight objects.
 * @param priorityInsights - An array of string identifiers from the backend.
 * @returns An array of rich Insight objects for the frontend.
 */
export const mapInsights = (priorityInsights: PriorityInsight[]): Insight[] => {
  return priorityInsights.map((insightId, index) => {
    const details = insightDetailsMap.get(insightId) || fallbackDetails[insightId];
    return {
      id: `${insightId}_${Date.now()}_${index}`,
      priority: priorityMap[insightId] || 'medium',
      ...details,
    };
  });
};
