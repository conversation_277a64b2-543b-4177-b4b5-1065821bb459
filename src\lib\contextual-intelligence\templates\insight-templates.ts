/**
 * LaundrySense Insight Templates
 * 
 * Comprehensive template library for contextual insight generation
 */

import { InsightTemplate } from '../types/insight-types';

export const INSIGHT_TEMPLATES: InsightTemplate[] = [
  // ============================================================================
  // MORNING CONTEXT TEMPLATES
  // ============================================================================
  {
    id: 'morning_daily_target',
    category: 'revenue_target',
    priority: 'high',
    title: 'Capai Target Penjualan Harian Rp{daily_target}',
    contextTemplate: 'Pagi adalah waktu krusial untuk mengatur fokus penjualan. Berdasarkan pola historis, target harian Anda adalah Rp{daily_target}. Saat ini sudah terkumpul Rp{current_revenue}.',
    actionTemplate: 'Pastikan kasir siap, monitor transaksi pembuka, dan promosikan paket "Cuci Hemat Pagi" untuk menarik pelanggan awal.',
    impactTemplate: 'Mencapai target awal hari memotivasi tim dan memastikan aliran kas yang stabil sepanjang hari.',
    timeEstimate: '15 mins',
    applicableContexts: {
      time: ['morning'],
      businessCycle: ['normal_season', 'peak_season'],
      dataQuality: ['high_confidence', 'medium_confidence']
    },
    requiredData: {
      patternData: ['revenue_patterns.daily_average'],
      realTimeData: ['transactions.today_revenue']
    },
    conditions: [
      { field: 'realTimeData.transactions.today_revenue', operator: 'lt', value: 'patternData.revenue_patterns.daily_average * 0.3' }
    ],
    tags: ['morning', 'revenue', 'target', 'motivation']
  },

  {
    id: 'morning_stock_check',
    category: 'inventory_management',
    priority: 'high',
    title: 'Cek Stok {material_name} - Tersisa {days_left} Hari',
    contextTemplate: 'Stok {material_name} Anda tinggal {current_stock} unit, cukup untuk {days_left} hari operasional. Pagi adalah waktu terbaik untuk memastikan stok mencukupi sepanjang hari.',
    actionTemplate: 'Periksa stok fisik {material_name}, hubungi supplier jika diperlukan, dan pertimbangkan pembelian darurat jika stok di bawah {minimum_threshold} unit.',
    impactTemplate: 'Mencegah kehabisan stok yang dapat mengganggu operasional dan mengurangi kepuasan pelanggan.',
    timeEstimate: '10 mins',
    applicableContexts: {
      time: ['morning'],
      dataQuality: ['high_confidence', 'medium_confidence']
    },
    requiredData: {
      realTimeData: ['materials.current_stock', 'materials.days_until_empty']
    },
    conditions: [
      { field: 'realTimeData.materials.days_until_empty', operator: 'lte', value: 3 }
    ],
    tags: ['morning', 'inventory', 'stock', 'prevention']
  },

  // NEW TEMPLATE FOR OVERDUE PICKUPS SUMMARY
  {
    id: 'alert_overdue_pickups_summary',
    category: 'customer_service',
    priority: 'high',
    title: 'Ada {count} Pesanan Terlambat Diambil!',
    // The placeholders {count}, {firstCustomerName}, {firstDaysOverdue}, {firstServiceType}, {firstWeightKg}, {firstPickupDateFormatted}, {firstCustomerPhone}
    // will need to be populated by the SmartInsightGenerator based on the realTimeData.overduePickups array.
    // The engine will need to access overduePickups.length for {count}
    // and properties of overduePickups[0] for the 'first...' placeholders.
    contextTemplate: 'Terdapat {count} pesanan yang sudah melewati tanggal pengambilan. Pelanggan pertama yang paling lama terlambat adalah {firstCustomerName} (Layanan: {firstServiceType} {firstWeightKg}kg, Telat: {firstDaysOverdue} hari, Janji Ambil: {firstPickupDateFormatted}). Segera tindak lanjuti untuk menjaga kepuasan pelanggan dan ketersediaan ruang.',
    actionTemplate: 'Lihat daftar lengkap pesanan terlambat dan segera hubungi pelanggan terkait. Prioritaskan {firstCustomerName} ({firstCustomerPhone}).',
    impactTemplate: 'Mengurangi risiko komplain, meningkatkan aliran kas, dan menjaga reputasi layanan yang baik.',
    timeEstimate: '10-30 menit',
    applicableContexts: {
      time: ['morning', 'midday', 'evening'],
      dataQuality: ['high_confidence', 'medium_confidence']
    },
    requiredData: {
      realTimeData: ['overduePickupItems'] 
    },
    conditions: [
      { field: 'realTimeData.overduePickupItems.length', operator: 'gt', value: 0 }
    ],
    variables: {
      count: 'realTimeData.overduePickupItems.length',
      firstCustomerName: 'realTimeData.overduePickupItems[0].customerName',
      firstDaysOverdue: 'realTimeData.overduePickupItems[0].daysOverdue',
      firstServiceType: 'realTimeData.overduePickupItems[0].serviceType',
      firstWeightKg: 'realTimeData.overduePickupItems[0].weightKg',
      firstPickupDateFormatted: 'realTimeData.overduePickupItems[0].pickupDate', // Assuming pickupDate is already formatted or will be handled by a formatter later
      firstCustomerPhone: 'realTimeData.overduePickupItems[0].customerPhone'
    },
    tags: ['customer_service', 'operations', 'alert', 'pickup', 'overdue']
  },

  // ============================================================================
  // MIDDAY CONTEXT TEMPLATES
  // ============================================================================
  {
    id: 'midday_peak_optimization',
    category: 'operational_efficiency',
    priority: 'high',
    title: 'Optimasi Jam Sibuk - {queue_length} Antrian Aktif',
    contextTemplate: 'Saat ini jam sibuk dengan {queue_length} pelanggan dalam antrian. Berdasarkan pola, jam {peak_hour} adalah puncak aktivitas hari ini.',
    actionTemplate: 'Aktifkan semua mesin yang tersedia, tambah staff di counter, dan informasikan estimasi waktu tunggu kepada pelanggan.',
    impactTemplate: 'Mengurangi waktu tunggu pelanggan dan meningkatkan throughput operasional hingga 25%.',
    timeEstimate: '5 mins',
    applicableContexts: {
      time: ['midday'],
      businessCycle: ['normal_season', 'peak_season']
    },
    requiredData: {
      realTimeData: ['operations.queue_length', 'operations.active_machines']
    },
    conditions: [
      { field: 'realTimeData.operations.queue_length', operator: 'gt', value: 5 }
    ],
    tags: ['midday', 'operations', 'efficiency', 'customer_service']
  },

  {
    id: 'midday_revenue_tracking',
    category: 'revenue_target',
    priority: 'medium',
    title: 'Progress Harian {revenue_percentage}% - Rp{current_revenue}',
    contextTemplate: 'Hingga saat ini Anda telah mencapai {revenue_percentage}% dari target harian (Rp{current_revenue} dari Rp{daily_target}). Performa ini {performance_status} dibanding rata-rata.',
    actionTemplate: 'Monitor transaksi per jam, fokus pada layanan premium jika di bawah target, atau pertahankan momentum jika di atas target.',
    impactTemplate: 'Memastikan pencapaian target harian dan memberikan visibilitas real-time terhadap performa bisnis.',
    timeEstimate: '3 mins',
    applicableContexts: {
      time: ['midday'],
      dataQuality: ['high_confidence', 'medium_confidence']
    },
    requiredData: {
      patternData: ['revenue_patterns.daily_average'],
      realTimeData: ['transactions.today_revenue']
    },
    conditions: [
      { field: 'realTimeData.transactions.today_revenue', operator: 'gt', value: 0 }
    ],
    tags: ['midday', 'revenue', 'tracking', 'performance']
  },

  // ============================================================================
  // EVENING CONTEXT TEMPLATES
  // ============================================================================
  {
    id: 'evening_customer_analysis',
    category: 'customer_service',
    priority: 'medium',
    title: 'Analisis Pelanggan Hari Ini - {new_customers} Baru, {returning_customers} Kembali',
    contextTemplate: 'Hari ini Anda melayani {new_customers} pelanggan baru dan {returning_customers} pelanggan kembali. Rasio ini menunjukkan {customer_trend} dalam akuisisi pelanggan.',
    actionTemplate: 'Tindak lanjuti pelanggan baru dengan welcome message, dan berikan apresiasi kepada pelanggan setia dengan poin loyalty.',
    impactTemplate: 'Meningkatkan customer retention rate dan membangun hubungan jangka panjang dengan pelanggan.',
    timeEstimate: '20 mins',
    applicableContexts: {
      time: ['evening'],
      userBehavior: ['normal_mode', 'growth_mode']
    },
    requiredData: {
      realTimeData: ['customers.new_customers_today', 'customers.returning_customers_today']
    },
    conditions: [
      { field: 'realTimeData.customers.new_customers_today', operator: 'gt', value: 0 }
    ],
    tags: ['evening', 'customers', 'analysis', 'retention']
  },

  {
    id: 'evening_cost_review',
    category: 'cost_optimization',
    priority: 'low',
    title: 'Review Efisiensi Biaya - Penggunaan {material_name}',
    contextTemplate: 'Penggunaan {material_name} hari ini adalah {daily_usage} unit, {efficiency_status} dibanding rata-rata harian {average_usage} unit.',
    actionTemplate: 'Evaluasi penggunaan material, cek apakah ada pemborosan, dan sesuaikan takaran untuk transaksi besok.',
    impactTemplate: 'Mengoptimalkan penggunaan material dapat menghemat biaya operasional hingga 15% per bulan.',
    timeEstimate: '15 mins',
    applicableContexts: {
      time: ['evening'],
      businessCycle: ['normal_season', 'low_season']
    },
    requiredData: {
      patternData: ['material_patterns.average_efficiency_score'],
      realTimeData: ['materials.usage_rate_per_day']
    },
    conditions: [
      { field: 'realTimeData.materials.usage_rate_per_day', operator: 'gt', value: 0 }
    ],
    tags: ['evening', 'cost', 'efficiency', 'optimization']
  },

  // ============================================================================
  // PLANNING CONTEXT TEMPLATES
  // ============================================================================
  {
    id: 'planning_growth_analysis',
    category: 'growth_opportunity',
    priority: 'medium',
    title: 'Analisis Peluang Pertumbuhan - Trend {growth_trend}',
    contextTemplate: 'Berdasarkan data 30 hari terakhir, bisnis Anda menunjukkan trend {growth_trend} dengan pertumbuhan {growth_rate}%. Ini adalah waktu yang tepat untuk perencanaan strategis.',
    actionTemplate: 'Analisis segmen pelanggan yang paling menguntungkan, identifikasi jam sibuk baru, dan rencanakan ekspansi layanan atau promosi.',
    impactTemplate: 'Perencanaan strategis yang tepat dapat meningkatkan revenue hingga 30% dalam 3 bulan ke depan.',
    timeEstimate: '45 mins',
    applicableContexts: {
      time: ['planning'],
      userBehavior: ['growth_mode', 'normal_mode'],
      dataQuality: ['high_confidence']
    },
    requiredData: {
      patternData: ['revenue_patterns.growth_trend', 'customer_patterns.average_loyalty_score']
    },
    conditions: [
      { field: 'patternData.revenue_patterns.growth_trend', operator: 'eq', value: 'increasing' }
    ],
    tags: ['planning', 'growth', 'strategy', 'analysis']
  },

  {
    id: 'planning_seasonal_prep',
    category: 'operational_efficiency',
    priority: 'medium',
    title: 'Persiapan Musiman - Stok & Strategi',
    contextTemplate: 'Berdasarkan pola historis, periode mendatang menunjukkan potensi perubahan permintaan. Persiapkan stok dan strategi operasional.',
    actionTemplate: 'Sesuaikan level stok material kunci, rencanakan promosi, dan siapkan strategi harga untuk periode mendatang.',
    impactTemplate: 'Persiapan yang matang dapat meningkatkan profit margin hingga 20% selama periode musiman.',
    timeEstimate: '1 hour',
    applicableContexts: {
      time: ['planning'],
      businessCycle: ['peak_season', 'low_season']
    },
    requiredData: {
      patternData: ['revenue_patterns.seasonal_factor_average']
    },
    conditions: [
      { field: 'patternData.revenue_patterns.seasonal_factor_average', operator: 'gt', value: 1.2 }, // e.g., high season approaching
      { field: 'patternData.revenue_patterns.seasonal_factor_average', operator: 'lt', value: 0.8 }  // e.g., low season approaching
    ],
    tags: ['planning', 'seasonal', 'preparation', 'strategy']
  },

  // --- TIME-BASED CONTEXTUAL INSIGHTS --- //
  // These insights are designed to appear at specific times of the day, providing routine guidance.

  {
    id: 'morning_operational_briefing',
    category: 'operational_planning',
    priority: 'normal',
    title: 'Selamat Pagi! Waktunya Persiapan Operasional',
    contextTemplate: 'Memulai hari dengan persiapan yang matang adalah kunci kesuksesan. Mari kita pastikan semuanya siap.',
    actionTemplate: 'Periksa status semua mesin cuci dan pengering. Pastikan stok deterjen dan pewangi cukup untuk hari ini.',
    impactTemplate: 'Mengurangi downtime mesin dan memastikan kelancaran layanan sepanjang hari.',
    timeEstimate: '20 menit',
    applicableContexts: {
      time: ['morning']
    },
    requiredData: {},
    conditions: [], // Always show in the morning if no higher priority insights are present
    tags: ['routine', 'morning', 'operations', 'planning']
  },

  {
    id: 'midday_monitoring_check',
    category: 'operational_monitoring',
    priority: 'low',
    title: 'Semua Terkendali di Siang Hari',
    contextTemplate: 'Operasional berjalan sesuai rencana. Ini adalah waktu yang baik untuk memantau aktivitas dan memastikan kualitas tetap terjaga.',
    actionTemplate: 'Lakukan pengecekan cepat di area layanan pelanggan dan ruang produksi. Pastikan tidak ada antrian yang menumpuk.',
    impactTemplate: 'Menjaga kepuasan pelanggan dan efisiensi operasional selama jam sibuk.',
    timeEstimate: '10 menit',
    applicableContexts: {
      time: ['midday']
    },
    requiredData: {},
    conditions: [], // A low-priority insight to show when things are calm
    tags: ['routine', 'midday', 'monitoring']
  },

  {
    id: 'evening_daily_recap',
    category: 'business_review',
    priority: 'normal',
    title: 'Waktunya Rekap Harian & Persiapan Besok',
    contextTemplate: 'Hari akan segera berakhir. Mari kita tinjau kinerja hari ini dan siapkan strategi untuk kesuksesan esok hari.',
    actionTemplate: 'Catat total pendapatan dan jumlah transaksi hari ini. Buat daftar tugas prioritas untuk besok pagi.',
    impactTemplate: 'Memastikan pembelajaran dari hari ini dapat diterapkan untuk meningkatkan kinerja di masa depan.',
    timeEstimate: '15 menit',
    applicableContexts: {
      time: ['evening']
    },
    requiredData: {},
    conditions: [], // Always show in the evening
    tags: ['routine', 'evening', 'review', 'planning']
  },

  // --- STRESS MODE INSIGHTS --- //
  // Insights designed to appear when the user is likely under high stress
  {
    id: 'stress_data_quality_alert',
    category: 'data_integrity',
    priority: 'critical',
    title: 'Peringatan Kualitas Data Kritis',
    contextTemplate: 'Terdeteksi masalah pada integritas data transaksi. Ini dapat mempengaruhi akurasi analisis dan pengambilan keputusan.',
    actionTemplate: 'Periksa sistem POS, pastikan semua transaksi tercatat lengkap, dan lakukan validasi data segera.',
    impactTemplate: 'Data yang akurat memastikan analisis yang tepat dan mencegah kerugian finansial akibat pencatatan yang salah.',
    timeEstimate: '30 mins',
    applicableContexts: {
      userBehavior: ['stress_mode'],
      dataQuality: ['low_confidence', 'medium_confidence']
    },
    requiredData: {
      contextObject: ['dataQuality']
    },
    conditions: [
      { field: 'contextObject.dataQuality', operator: 'eq', value: 'low_confidence' }
    ],
    tags: ['stress', 'data_quality', 'urgent', 'system']
  },

  {
    id: 'stress_stock_alert',
    category: 'inventory_management',
    priority: 'high',
    title: 'URGENT: Stok Kritis!',
    contextTemplate: 'Beberapa bahan penting berada di bawah ambang batas minimum. Tindakan segera diperlukan untuk menghindari penghentian operasional.',
    actionTemplate: 'Buka laporan stok sekarang untuk melihat detailnya dan segera hubungi pemasok.',
    impactTemplate: 'Mencegah penghentian operasional yang dapat menyebabkan kerugian revenue dan kekecewaan pelanggan.',
    timeEstimate: '10 mins',
    applicableContexts: {
      userBehavior: ['stress_mode']
    },
    requiredData: {
      realTimeData: ['materials']
    },
    conditions: [
      { field: 'realTimeData.materials.length', operator: 'gt', value: 0 } // This will trigger if any low-stock material is found
    ],
    tags: ['stress', 'urgent', 'inventory', 'critical']
  }
];

// ============================================================================
// TEMPLATE UTILITIES
// ============================================================================

export const TEMPLATE_CATEGORIES = {
  revenue_target: 'Target Pendapatan',
  inventory_management: 'Manajemen Inventori',
  customer_service: 'Layanan Pelanggan',
  operational_efficiency: 'Efisiensi Operasional',
  data_quality: 'Kualitas Data',
  growth_opportunity: 'Peluang Pertumbuhan',
  cost_optimization: 'Optimasi Biaya',
  staff_management: 'Manajemen Staff'
};

export const PRIORITY_WEIGHTS = {
  high: 3,
  medium: 2,
  low: 1
};

export const TIME_ESTIMATES = {
  '5 mins': 5,
  '10 mins': 10,
  '15 mins': 15,
  '20 mins': 20,
  '30 mins': 30,
  '45 mins': 45,
  '1 hour': 60,
  '2 hours': 120
};
