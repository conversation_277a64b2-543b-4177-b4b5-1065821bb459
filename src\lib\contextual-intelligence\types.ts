/**
 * LaundrySense Contextual Intelligence Types
 * 
 * Type definitions for the Context Detection System and related
 * contextual intelligence components.
 */

// ============================================================================
// INPUT TYPES
// ============================================================================

export interface UserActivity {
  timestamp: string;
  action: string;
  user_id: string;
  metadata?: Record<string, any>;
}

export interface HistoricalBusinessData {
  monthly_transactions: {
    month: string;
    count: number;
    revenue: number;
  }[];
  material_usage_averages: {
    material_id: string;
    material_name: string;
    average_monthly_usage: number;
    category: string;
  }[];
  customer_metrics: {
    total_customers: number;
    active_customers_last_30_days: number;
    average_transaction_value: number;
    customer_retention_rate: number;
  };
  revenue_trends: {
    daily_average: number;
    weekly_average: number;
    monthly_average: number;
    growth_rate_percentage: number;
  };
}

export interface DataCompletenessMetrics {
  transactions: {
    last_updated: string;
    completeness_percentage: number;
    total_records: number;
    missing_fields_count: number;
  };
  customers: {
    last_updated: string;
    completeness_percentage: number;
    total_records: number;
    missing_fields_count: number;
  };
  materials: {
    last_updated: string;
    completeness_percentage: number;
    total_records: number;
    missing_fields_count: number;
  };
  patterns: {
    last_calculated: string;
    customer_patterns_count: number;
    material_patterns_count: number;
    revenue_trends_count: number;
    average_confidence_score: number;
  };
}

export interface PatternData {
  customer_patterns: {
    total_analyzed?: number;
    high_confidence_count?: number;
    average_loyalty_score: number;
    average_frequency_score?: number;
    seasonal_variance_average?: number;
    segment_distribution?: Record<string, number>;
  };
  material_patterns: {
    total_analyzed?: number;
    reorder_recommendations_count?: number;
    average_efficiency_score: number;
    stock_alerts_count?: number;
    common_combinations?: string[][];
  };
  revenue_patterns: {
    peak_days_count?: number;
    growth_trend: 'increasing' | 'decreasing' | 'stable';
    seasonal_factor_average: number;
    revenue_volatility?: number;
    daily_average?: number;
    hourly_distribution?: Record<string, number>;
  };
}

export interface ContextDetectionInput {
  currentTimestamp: Date;
  recentUserActivityLog: UserActivity[];
  historicalBusinessData: HistoricalBusinessData;
  dataCompletenessMetrics: DataCompletenessMetrics;
  patternData: PatternData;
}

// ============================================================================
// CONTEXT MODE TYPE
// ============================================================================

export type ContextMode = 'morning' | 'afternoon' | 'evening' | 'night' | 'unknown';

// ============================================================================
// OUTPUT TYPES
// ============================================================================

export interface TimeContext {
  timeOfDay: ContextMode;
  date: Date;
}

export interface BusinessCycleContext {
  status?: string;
  trend?: 'peak' | 'off-peak' | 'normal';
  [key: string]: any;
}

export interface UserBehaviorContext {
  activityLevel?: 'high' | 'medium' | 'low';
  recentInteractions?: number;
  [key: string]: any;
}

export interface DataQualityContext {
  completenessScore?: number; // 0-1
  freshness?: 'current' | 'stale';
  [key: string]: any;
}

export interface CurrentContextObject {
  time: TimeContext;
  businessCycle: BusinessCycleContext;
  userBehavior: UserBehaviorContext;
  dataQuality: DataQualityContext;
}

export type DashboardMode = 
  | 'operational_overview'
  | 'strategic_planning'
  | 'alert_mode'
  | 'growth_analysis'
  | 'maintenance_mode';

export type PriorityInsight = 
  | 'daily_sales_target'
  | 'material_restock_alert'
  | 'customer_retention_suggestion'
  | 'peak_hour_optimization'
  | 'seasonal_preparation'
  | 'cost_efficiency_review'
  | 'growth_opportunity_analysis'
  | 'data_quality_improvement'
  | 'inventory_optimization'
  | 'customer_behavior_analysis';

export interface ContextDetectionResult {
  currentContextObject: CurrentContextObject;
  recommendedDashboardMode: DashboardMode;
  priorityInsightsList: PriorityInsight[];
  dataQualityScore: number; // 0-100
  contextConfidence: number; // 0-1, confidence in context detection accuracy
  detectionTimestamp: Date;
  contextReasons: {
    time: string;
    businessCycle: string;
    userBehavior: string;
    dataQuality: string;
  };
}

// ============================================================================
// CONFIGURATION TYPES
// ============================================================================

export interface ContextDetectionConfig {
  timeZone: string;
  businessHours: {
    start: number; // 24-hour format
    end: number;
  };
  seasonalFactorThresholds: {
    peak: number;
    low: number;
  };
  dataQualityThresholds: {
    high: number;
    medium: number;
  };
  userActivityThresholds: {
    stress_mode_actions_per_hour: number;
    growth_mode_analysis_actions: number;
  };
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export interface TimeAnalysis {
  hour: number;
  dayOfWeek: number;
  isWeekend: boolean;
  isBusinessHours: boolean;
  timeContext: TimeContext;
}

export interface BusinessCycleAnalysis {
  currentSeasonalFactor: number;
  recentGrowthRate: number;
  transactionVolumeTrend: 'increasing' | 'decreasing' | 'stable';
  businessCycleContext: BusinessCycleContext;
}

export interface UserBehaviorAnalysis {
  recentActivityCount: number;
  errorRate: number;
  analysisActionCount: number;
  bulkActionCount: number;
  behaviorContext: UserBehaviorContext;
}

export interface DataQualityAnalysis {
  overallCompleteness: number;
  dataFreshness: number;
  patternReliability: number;
  qualityContext: DataQualityContext;
}
