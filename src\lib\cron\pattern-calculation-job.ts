import cron from 'node-cron';
import { PatternCalculationService } from '../pattern-analysis/pattern-calculator';

export class PatternCalculationJob {
  private calculationService: PatternCalculationService;
  private isRunning: boolean = false;

  constructor() {
    this.calculationService = new PatternCalculationService();
  }

  // Schedule pattern calculations to run daily at 2 AM
  startDailyCalculation() {
    console.log('📅 Scheduling daily pattern calculation job...');
    
    cron.schedule('0 2 * * *', async () => {
      if (this.isRunning) {
        console.log('⏳ Pattern calculation already running, skipping...');
        return;
      }

      console.log('🚀 Starting scheduled pattern calculation...');
      await this.runFullCalculation();
    }, {
      timezone: 'Asia/Jakarta' // Adjust timezone as needed
    });

    console.log('✅ Daily pattern calculation job scheduled for 2:00 AM');
  }

  // Schedule pattern calculations to run weekly on Sunday at 3 AM
  startWeeklyCalculation() {
    console.log('📅 Scheduling weekly pattern calculation job...');
    
    cron.schedule('0 3 * * 0', async () => {
      if (this.isRunning) {
        console.log('⏳ Pattern calculation already running, skipping...');
        return;
      }

      console.log('🚀 Starting scheduled weekly pattern calculation...');
      await this.runFullCalculation();
    }, {
      timezone: 'Asia/Jakarta'
    });

    console.log('✅ Weekly pattern calculation job scheduled for Sunday 3:00 AM');
  }

  // Run calculation manually
  async runFullCalculation(): Promise<void> {
    if (this.isRunning) {
      console.log('⏳ Pattern calculation already in progress');
      return;
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      console.log('🔄 Starting full pattern calculation...');
      
      const results = await this.calculationService.calculateAllPatterns();
      
      const totalTime = Date.now() - startTime;
      const successCount = results.filter(r => r.status === 'completed').length;
      const failureCount = results.filter(r => r.status === 'failed').length;

      console.log('📊 Pattern calculation summary:');
      console.log(`   ✅ Successful: ${successCount}`);
      console.log(`   ❌ Failed: ${failureCount}`);
      console.log(`   ⏱️  Total time: ${totalTime}ms`);

      // Log detailed results
      results.forEach(result => {
        const status = result.status === 'completed' ? '✅' : '❌';
        console.log(`   ${status} ${result.calculation_type}: ${result.records_updated} records updated in ${result.execution_time_ms}ms`);
        
        if (result.error_message) {
          console.log(`      Error: ${result.error_message}`);
        }
      });

    } catch (error) {
      console.error('❌ Pattern calculation failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // Run only customer pattern calculation
  async runCustomerPatternCalculation(): Promise<void> {
    if (this.isRunning) {
      console.log('⏳ Pattern calculation already in progress');
      return;
    }

    this.isRunning = true;

    try {
      console.log('👥 Starting customer pattern calculation...');
      const result = await this.calculationService.calculateCustomerPatterns();
      
      const status = result.status === 'completed' ? '✅' : '❌';
      console.log(`${status} Customer patterns: ${result.records_updated} records updated in ${result.execution_time_ms}ms`);
      
      if (result.error_message) {
        console.log(`Error: ${result.error_message}`);
      }

    } catch (error) {
      console.error('❌ Customer pattern calculation failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // Run only material pattern calculation
  async runMaterialPatternCalculation(): Promise<void> {
    if (this.isRunning) {
      console.log('⏳ Pattern calculation already in progress');
      return;
    }

    this.isRunning = true;

    try {
      console.log('🧴 Starting material pattern calculation...');
      const result = await this.calculationService.calculateMaterialPatterns();
      
      const status = result.status === 'completed' ? '✅' : '❌';
      console.log(`${status} Material patterns: ${result.records_updated} records updated in ${result.execution_time_ms}ms`);
      
      if (result.error_message) {
        console.log(`Error: ${result.error_message}`);
      }

    } catch (error) {
      console.error('❌ Material pattern calculation failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // Run only revenue trend calculation
  async runRevenueTrendCalculation(): Promise<void> {
    if (this.isRunning) {
      console.log('⏳ Pattern calculation already in progress');
      return;
    }

    this.isRunning = true;

    try {
      console.log('💰 Starting revenue trend calculation...');
      const result = await this.calculationService.calculateRevenueTrends();
      
      const status = result.status === 'completed' ? '✅' : '❌';
      console.log(`${status} Revenue trends: ${result.records_updated} records updated in ${result.execution_time_ms}ms`);
      
      if (result.error_message) {
        console.log(`Error: ${result.error_message}`);
      }

    } catch (error) {
      console.error('❌ Revenue trend calculation failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  // Get calculation status
  getStatus(): { isRunning: boolean } {
    return { isRunning: this.isRunning };
  }

  // Stop all scheduled jobs
  stopAllJobs(): void {
    cron.getTasks().forEach((task, name) => {
      task.stop();
      console.log(`🛑 Stopped cron job: ${name}`);
    });
  }
}

// Export singleton instance
export const patternCalculationJob = new PatternCalculationJob();
