import { PrismaClient } from '@prisma/client';

export async function getRealTimeKpis(prisma: PrismaClient) {
  // This logic is adapted from the /api/kpis/real-time route
  const totalRevenue = await prisma.transaction.aggregate({
    _sum: { price: true },
  });

  const totalTransactions = await prisma.transaction.count();

  const averageTransactionValue = totalTransactions > 0
    ? (totalRevenue._sum.price || 0) / totalTransactions
    : 0;

  const activeCustomers = await prisma.transaction.groupBy({
    by: ['customer_id'],
  });

  return {
    total_revenue: totalRevenue._sum.price || 0,
    total_transactions: totalTransactions,
    average_transaction_value: averageTransactionValue,
    active_customers: activeCustomers.length,
  };
}
