import { PrismaClient } from '@prisma/client';

export async function getPatternData(prisma: PrismaClient) {
  // This logic is adapted from the /api/patterns/all route
  const revenuePatterns = await prisma.revenueTrend.findMany({
    orderBy: { date: 'desc' },
    take: 1,
  });

  const customerPatterns = await prisma.customerPattern.findMany({
    orderBy: { calculated_at: 'desc' },
    take: 1,
  });

  const materialUsagePatterns = await prisma.materialUsagePattern.findMany({
    orderBy: { calculated_at: 'desc' },
    take: 1,
  });

  // Return the first record of each, or null if not found
  return {
    revenue_patterns: revenuePatterns[0] || null,
    customer_patterns: customerPatterns[0] || null,
    material_usage_patterns: materialUsagePatterns[0] || null,
  };
}
