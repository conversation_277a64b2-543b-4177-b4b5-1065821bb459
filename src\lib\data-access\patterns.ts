import { PrismaClient } from '@prisma/client';

export async function getPatternData(prisma: PrismaClient) {
  // This logic is adapted from the /api/patterns/all route
  const revenuePatterns = await prisma.revenueTrends.findMany({
    orderBy: { calculation_date: 'desc' },
    take: 1,
  });

  const customerPatterns = await prisma.customerPatterns.findMany({
    orderBy: { last_calculated: 'desc' },
    take: 1,
  });

  const materialUsagePatterns = await prisma.materialUsagePatterns.findMany({
    orderBy: { last_calculated: 'desc' },
    take: 1,
  });

  // Return the first record of each, or null if not found
  return {
    revenue_patterns: revenuePatterns[0] || null,
    customer_patterns: customerPatterns[0] || null,
    material_usage_patterns: materialUsagePatterns[0] || null,
  };
}
