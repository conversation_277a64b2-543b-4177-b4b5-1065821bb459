import { PrismaClient, ServiceType, DayType } from '@/generated/prisma';

const prisma = new PrismaClient();

export interface CustomerPatternResult {
  customer_id: string;
  calculated_frequency: number;
  frequency_trend: 'increasing' | 'decreasing' | 'stable';
  last_transaction_days_ago: number;
  preferred_service_type: ServiceType;
  preferred_service_confidence: number;
  average_weight_kg: number;
  average_spending: number;
  seasonal_activity_json: Record<string, number>;
  peak_months: string;
  low_months: string;
  seasonal_variance: number;
  loyalty_score: number;
  value_score: number;
  predictability_score: number;
  confidence_score: number;
  data_points_count: number;
  analysis_period_days: number;
}

export class CustomerPatternAnalyzer {
  private readonly MINIMUM_TRANSACTIONS = 3;
  private readonly ANALYSIS_PERIOD_DAYS = 365; // Analyze last year
  private readonly FREQUENCY_TREND_THRESHOLD = 0.1; // 10% change threshold

  async analyzeCustomerPattern(customerId: string): Promise<CustomerPatternResult | null> {
    const customer = await prisma.customer.findUnique({
      where: { id: customerId },
      include: {
        transactions: {
          where: {
            transaction_date: {
              gte: new Date(Date.now() - this.ANALYSIS_PERIOD_DAYS * 24 * 60 * 60 * 1000)
            }
          },
          orderBy: { transaction_date: 'asc' }
        }
      }
    });

    if (!customer || customer.transactions.length < this.MINIMUM_TRANSACTIONS) {
      return null;
    }

    const transactions = customer.transactions;
    const analysisStartDate = new Date(Date.now() - this.ANALYSIS_PERIOD_DAYS * 24 * 60 * 60 * 1000);
    const analysisEndDate = new Date();
    const analysisPeriodDays = Math.ceil((analysisEndDate.getTime() - analysisStartDate.getTime()) / (24 * 60 * 60 * 1000));

    // Calculate frequency metrics
    const frequencyMetrics = this.calculateFrequencyMetrics(transactions, analysisPeriodDays);
    
    // Calculate preference metrics
    const preferenceMetrics = this.calculatePreferenceMetrics(transactions);
    
    // Calculate seasonal patterns
    const seasonalMetrics = this.calculateSeasonalMetrics(transactions);
    
    // Calculate behavioral scores
    const behavioralScores = this.calculateBehavioralScores(transactions, customer.registration_date);
    
    // Calculate confidence score
    const confidenceScore = this.calculateConfidenceScore(transactions.length, analysisPeriodDays, seasonalMetrics.seasonal_variance);

    return {
      customer_id: customerId,
      ...frequencyMetrics,
      ...preferenceMetrics,
      ...seasonalMetrics,
      ...behavioralScores,
      confidence_score: confidenceScore,
      data_points_count: transactions.length,
      analysis_period_days: analysisPeriodDays
    };
  }

  private calculateFrequencyMetrics(transactions: any[], analysisPeriodDays: number) {
    const transactionCount = transactions.length;
    const monthsInPeriod = analysisPeriodDays / 30.44; // Average days per month
    const calculated_frequency = transactionCount / monthsInPeriod;

    // Calculate trend by comparing first and second half of period
    const midPoint = Math.floor(transactions.length / 2);
    const firstHalf = transactions.slice(0, midPoint);
    const secondHalf = transactions.slice(midPoint);

    const firstHalfFreq = firstHalf.length / (monthsInPeriod / 2);
    const secondHalfFreq = secondHalf.length / (monthsInPeriod / 2);
    const frequencyChange = (secondHalfFreq - firstHalfFreq) / firstHalfFreq;

    let frequency_trend: 'increasing' | 'decreasing' | 'stable';
    if (frequencyChange > this.FREQUENCY_TREND_THRESHOLD) {
      frequency_trend = 'increasing';
    } else if (frequencyChange < -this.FREQUENCY_TREND_THRESHOLD) {
      frequency_trend = 'decreasing';
    } else {
      frequency_trend = 'stable';
    }

    const lastTransaction = transactions[transactions.length - 1];
    const last_transaction_days_ago = Math.ceil(
      (Date.now() - new Date(lastTransaction.transaction_date).getTime()) / (24 * 60 * 60 * 1000)
    );

    return {
      calculated_frequency,
      frequency_trend,
      last_transaction_days_ago
    };
  }

  private calculatePreferenceMetrics(transactions: any[]) {
    // Service type preference
    const serviceTypeCounts = transactions.reduce((acc, t) => {
      acc[t.service_type] = (acc[t.service_type] || 0) + 1;
      return acc;
    }, {} as Record<ServiceType, number>);

    const mostUsedService = Object.entries(serviceTypeCounts)
      .sort(([,a], [,b]) => b - a)[0];
    
    const preferred_service_type = mostUsedService[0] as ServiceType;
    const preferred_service_confidence = mostUsedService[1] / transactions.length;

    // Weight and spending averages
    const totalWeight = transactions.reduce((sum, t) => sum + t.weight_kg, 0);
    const totalSpending = transactions.reduce((sum, t) => sum + t.price, 0);
    
    const average_weight_kg = totalWeight / transactions.length;
    const average_spending = totalSpending / transactions.length;

    return {
      preferred_service_type,
      preferred_service_confidence,
      average_weight_kg,
      average_spending
    };
  }

  private calculateSeasonalMetrics(transactions: any[]) {
    // Group transactions by month
    const monthlyActivity = transactions.reduce((acc, t) => {
      const month = new Date(t.transaction_date).getMonth() + 1; // 1-12
      acc[month] = (acc[month] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);

    // Fill missing months with 0
    for (let month = 1; month <= 12; month++) {
      if (!monthlyActivity[month]) {
        monthlyActivity[month] = 0;
      }
    }

    // Calculate seasonal variance
    const monthlyValues = Object.values(monthlyActivity);
    const mean = monthlyValues.reduce((sum, val) => sum + val, 0) / 12;
    const variance = monthlyValues.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / 12;
    const seasonal_variance = Math.sqrt(variance) / mean; // Coefficient of variation

    // Identify peak and low months
    const sortedMonths = Object.entries(monthlyActivity)
      .sort(([,a], [,b]) => b - a);
    
    const peak_months = sortedMonths.slice(0, 3).map(([month]) => month).join(',');
    const low_months = sortedMonths.slice(-3).map(([month]) => month).join(',');

    return {
      seasonal_activity_json: monthlyActivity,
      peak_months,
      low_months,
      seasonal_variance
    };
  }

  private calculateBehavioralScores(transactions: any[], registrationDate: Date) {
    const now = new Date();
    const customerLifetimeDays = (now.getTime() - registrationDate.getTime()) / (24 * 60 * 60 * 1000);
    
    // Loyalty Score: Based on consistency and lifetime
    const expectedTransactions = customerLifetimeDays / 30; // Expected monthly transactions
    const actualTransactions = transactions.length;
    const loyalty_score = Math.min(actualTransactions / Math.max(expectedTransactions, 1), 1);

    // Value Score: Based on spending patterns
    const totalSpending = transactions.reduce((sum, t) => sum + t.price, 0);
    const averageTransactionValue = totalSpending / transactions.length;
    // Normalize against typical transaction value (assume 50000 as baseline)
    const value_score = Math.min(averageTransactionValue / 50000, 1);

    // Predictability Score: Based on regularity of transactions
    if (transactions.length < 2) {
      return { loyalty_score, value_score, predictability_score: 0 };
    }

    const intervals = [];
    for (let i = 1; i < transactions.length; i++) {
      const prevDate = new Date(transactions[i-1].transaction_date);
      const currDate = new Date(transactions[i].transaction_date);
      const daysDiff = (currDate.getTime() - prevDate.getTime()) / (24 * 60 * 60 * 1000);
      intervals.push(daysDiff);
    }

    const meanInterval = intervals.reduce((sum, val) => sum + val, 0) / intervals.length;
    const intervalVariance = intervals.reduce((sum, val) => sum + Math.pow(val - meanInterval, 2), 0) / intervals.length;
    const coefficientOfVariation = Math.sqrt(intervalVariance) / meanInterval;
    const predictability_score = Math.max(0, 1 - coefficientOfVariation / 2); // Lower CV = higher predictability

    return {
      loyalty_score,
      value_score,
      predictability_score
    };
  }

  private calculateConfidenceScore(dataPoints: number, analysisPeriod: number, seasonalVariance: number): number {
    // Data quantity factor (0-1)
    const dataQuantityFactor = Math.min(dataPoints / 20, 1); // Ideal: 20+ transactions
    
    // Time period factor (0-1)
    const timePeriodFactor = Math.min(analysisPeriod / 365, 1); // Ideal: 1+ year of data
    
    // Stability factor (0-1) - lower variance = higher confidence
    const stabilityFactor = Math.max(0, 1 - seasonalVariance);
    
    // Weighted average
    return (dataQuantityFactor * 0.4 + timePeriodFactor * 0.3 + stabilityFactor * 0.3);
  }

  async analyzeAllCustomers(): Promise<CustomerPatternResult[]> {
    const customers = await prisma.customer.findMany({
      select: { id: true }
    });

    const results: CustomerPatternResult[] = [];
    
    for (const customer of customers) {
      try {
        const pattern = await this.analyzeCustomerPattern(customer.id);
        if (pattern) {
          results.push(pattern);
        }
      } catch (error) {
        console.error(`Error analyzing customer ${customer.id}:`, error);
      }
    }

    return results;
  }
}
