import { PrismaClient, DayType, TimeOfDay } from '@prisma/client';

const prisma = new PrismaClient();

export interface MaterialUsagePatternResult {
  material_id: string;
  average_consumption_rate: number;
  consumption_trend: 'increasing' | 'decreasing' | 'stable';
  peak_usage_day_type: DayType;
  peak_usage_time: TimeOfDay;
  seasonal_adjustment_json: Record<string, number>;
  peak_consumption_months: string;
  low_consumption_months: string;
  seasonal_multiplier: number;
  cost_efficiency_score: number;
  usage_efficiency_score: number;
  waste_indicator: number;
  predicted_days_to_empty: number;
  reorder_recommendation: boolean;
  optimal_stock_level: number;
  confidence_score: number;
  data_points_count: number;
  analysis_period_days: number;
}

export class MaterialPatternAnalyzer {
  private readonly MINIMUM_USAGE_RECORDS = 2; // Lowered from 5 to 2 for seed data
  private readonly ANALYSIS_PERIOD_DAYS = 180; // Analyze last 6 months
  private readonly TREND_THRESHOLD = 0.15; // 15% change threshold
  private readonly REORDER_THRESHOLD_DAYS = 14; // Reorder when < 14 days left

  async analyzeMaterialPattern(materialId: string): Promise<MaterialUsagePatternResult | null> {
    const material = await prisma.material.findUnique({
      where: { id: materialId },
      include: {
        transaction_materials: {
          include: {
            transaction: {
              select: {
                transaction_date: true,
                context_day_type: true,
                context_time_of_day: true,
                weight_kg: true,
                price: true
              }
            }
          },
          where: {
            transaction: {
              transaction_date: {
                gte: new Date(Date.now() - this.ANALYSIS_PERIOD_DAYS * 24 * 60 * 60 * 1000)
              }
            }
          },
          orderBy: {
            transaction: { transaction_date: 'asc' }
          }
        }
      }
    });

    if (!material) {
      console.log(`[Material Analysis] Material ${materialId} not found`);
      return null;
    }

    if (material.transaction_materials.length < this.MINIMUM_USAGE_RECORDS) {
      console.log(`[Material Analysis] ${material.material_name}: Only ${material.transaction_materials.length} usage records (minimum: ${this.MINIMUM_USAGE_RECORDS})`);
      return null;
    }

    console.log(`[Material Analysis] Analyzing ${material.material_name}: ${material.transaction_materials.length} usage records`);

    const usageRecords = material.transaction_materials;
    const analysisPeriodDays = this.ANALYSIS_PERIOD_DAYS;

    // Calculate consumption metrics
    const consumptionMetrics = this.calculateConsumptionMetrics(usageRecords, analysisPeriodDays);

    // Calculate usage patterns
    const usagePatterns = this.calculateUsagePatterns(usageRecords);

    // Calculate seasonal patterns
    const seasonalMetrics = this.calculateSeasonalMetrics(usageRecords);

    // Calculate efficiency metrics
    const efficiencyMetrics = this.calculateEfficiencyMetrics(usageRecords, material);

    // Calculate stock predictions
    const stockPredictions = this.calculateStockPredictions(
      material,
      consumptionMetrics.average_consumption_rate,
      seasonalMetrics.seasonal_multiplier
    );

    // Calculate confidence score
    const confidenceScore = this.calculateConfidenceScore(
      usageRecords.length,
      analysisPeriodDays,
      seasonalMetrics.seasonal_adjustment_json
    );

    return {
      material_id: materialId,
      ...consumptionMetrics,
      ...usagePatterns,
      ...seasonalMetrics,
      ...efficiencyMetrics,
      ...stockPredictions,
      confidence_score: confidenceScore,
      data_points_count: usageRecords.length,
      analysis_period_days: analysisPeriodDays
    };
  }

  private calculateConsumptionMetrics(usageRecords: any[], analysisPeriodDays: number) {
    const totalConsumption = usageRecords.reduce((sum, record) => sum + record.quantity_used, 0);
    const average_consumption_rate = totalConsumption / analysisPeriodDays;

    // Calculate trend by comparing first and second half
    const midPoint = Math.floor(usageRecords.length / 2);
    const firstHalf = usageRecords.slice(0, midPoint);
    const secondHalf = usageRecords.slice(midPoint);

    const firstHalfConsumption = firstHalf.reduce((sum, r) => sum + r.quantity_used, 0);
    const secondHalfConsumption = secondHalf.reduce((sum, r) => sum + r.quantity_used, 0);

    const firstHalfRate = firstHalfConsumption / (analysisPeriodDays / 2);
    const secondHalfRate = secondHalfConsumption / (analysisPeriodDays / 2);

    const trendChange = firstHalfRate > 0 ? (secondHalfRate - firstHalfRate) / firstHalfRate : 0;

    let consumption_trend: 'increasing' | 'decreasing' | 'stable';
    if (trendChange > this.TREND_THRESHOLD) {
      consumption_trend = 'increasing';
    } else if (trendChange < -this.TREND_THRESHOLD) {
      consumption_trend = 'decreasing';
    } else {
      consumption_trend = 'stable';
    }

    return {
      average_consumption_rate,
      consumption_trend
    };
  }

  private calculateUsagePatterns(usageRecords: any[]) {
    // Analyze day type patterns
    const dayTypeUsage = usageRecords.reduce((acc, record) => {
      const dayType = record.transaction.context_day_type;
      acc[dayType] = (acc[dayType] || 0) + record.quantity_used;
      return acc;
    }, {} as Record<DayType, number>);

    const peak_usage_day_type = Object.entries(dayTypeUsage)
      .sort(([,a], [,b]) => b - a)[0]?.[0] as DayType || DayType.WEEKDAY;

    // Analyze time patterns
    const timeUsage = usageRecords.reduce((acc, record) => {
      const timeOfDay = record.transaction.context_time_of_day;
      acc[timeOfDay] = (acc[timeOfDay] || 0) + record.quantity_used;
      return acc;
    }, {} as Record<TimeOfDay, number>);

    const peak_usage_time = Object.entries(timeUsage)
      .sort(([,a], [,b]) => b - a)[0]?.[0] as TimeOfDay || TimeOfDay.MORNING;

    return {
      peak_usage_day_type,
      peak_usage_time
    };
  }

  private calculateSeasonalMetrics(usageRecords: any[]) {
    // Group usage by month
    const monthlyUsage = usageRecords.reduce((acc, record) => {
      const month = new Date(record.transaction.transaction_date).getMonth() + 1;
      acc[month] = (acc[month] || 0) + record.quantity_used;
      return acc;
    }, {} as Record<number, number>);

    // Fill missing months with 0
    for (let month = 1; month <= 12; month++) {
      if (!monthlyUsage[month]) {
        monthlyUsage[month] = 0;
      }
    }

    // Calculate seasonal multiplier (peak vs average)
    const monthlyValues = Object.values(monthlyUsage);
    const averageMonthlyUsage = monthlyValues.reduce((sum, val) => sum + val, 0) / 12;
    const maxMonthlyUsage = Math.max(...monthlyValues);
    const seasonal_multiplier = averageMonthlyUsage > 0 ? maxMonthlyUsage / averageMonthlyUsage : 1;

    // Identify peak and low months
    const sortedMonths = Object.entries(monthlyUsage)
      .sort(([,a], [,b]) => b - a);

    const peak_consumption_months = sortedMonths.slice(0, 3).map(([month]) => month).join(',');
    const low_consumption_months = sortedMonths.slice(-3).map(([month]) => month).join(',');

    return {
      seasonal_adjustment_json: monthlyUsage,
      peak_consumption_months,
      low_consumption_months,
      seasonal_multiplier
    };
  }

  private calculateEfficiencyMetrics(usageRecords: any[], material: any) {
    // Cost efficiency: cost per transaction
    const totalCost = usageRecords.reduce((sum, record) => sum + record.cost_at_time, 0);
    const totalTransactions = usageRecords.length;
    const avgCostPerTransaction = totalCost / totalTransactions;

    // Benchmark against material cost (lower is better)
    const benchmarkCost = material.cost_per_unit * material.usage_rate_per_transaction;
    const cost_efficiency_score = benchmarkCost > 0 ?
      Math.max(0, 1 - (avgCostPerTransaction - benchmarkCost) / benchmarkCost) : 0.5;

    // Usage efficiency: usage per kg of laundry
    const totalWeight = usageRecords.reduce((sum, record) =>
      sum + record.transaction.weight_kg, 0);
    const totalUsage = usageRecords.reduce((sum, record) => sum + record.quantity_used, 0);
    const actualUsagePerKg = totalUsage / totalWeight;

    // Compare with expected usage rate
    const expectedUsagePerKg = material.usage_rate_per_kg;
    const usage_efficiency_score = expectedUsagePerKg > 0 ?
      Math.max(0, 1 - Math.abs(actualUsagePerKg - expectedUsagePerKg) / expectedUsagePerKg) : 0.5;

    // Waste indicator: variance in usage patterns
    const usagePerKgValues = usageRecords.map(record =>
      record.quantity_used / record.transaction.weight_kg);
    const meanUsagePerKg = usagePerKgValues.reduce((sum, val) => sum + val, 0) / usagePerKgValues.length;
    const variance = usagePerKgValues.reduce((sum, val) =>
      sum + Math.pow(val - meanUsagePerKg, 2), 0) / usagePerKgValues.length;
    const waste_indicator = Math.sqrt(variance) / meanUsagePerKg; // Coefficient of variation

    return {
      cost_efficiency_score,
      usage_efficiency_score,
      waste_indicator
    };
  }

  private calculateStockPredictions(material: any, consumptionRate: number, seasonalMultiplier: number) {
    const currentStock = material.current_stock_unit;
    const adjustedConsumptionRate = consumptionRate * seasonalMultiplier;

    const predicted_days_to_empty = adjustedConsumptionRate > 0 ?
      Math.floor(currentStock / adjustedConsumptionRate) : 999;

    const reorder_recommendation = predicted_days_to_empty <= this.REORDER_THRESHOLD_DAYS;

    // Optimal stock level: 30 days of consumption + safety buffer
    const safetyBuffer = 1.2; // 20% safety buffer
    const optimal_stock_level = adjustedConsumptionRate * 30 * safetyBuffer;

    return {
      predicted_days_to_empty,
      reorder_recommendation,
      optimal_stock_level
    };
  }

  private calculateConfidenceScore(dataPoints: number, analysisPeriod: number, seasonalData: Record<string, number>): number {
    // Data quantity factor
    const dataQuantityFactor = Math.min(dataPoints / 30, 1); // Ideal: 30+ usage records

    // Time period factor
    const timePeriodFactor = Math.min(analysisPeriod / 180, 1); // Ideal: 6+ months

    // Seasonal data completeness
    const monthsWithData = Object.values(seasonalData).filter(val => val > 0).length;
    const seasonalCompletenessFactor = monthsWithData / 12;

    // Weighted average
    return (dataQuantityFactor * 0.4 + timePeriodFactor * 0.3 + seasonalCompletenessFactor * 0.3);
  }

  async analyzeAllMaterials(): Promise<MaterialUsagePatternResult[]> {
    const materials = await prisma.material.findMany({
      select: { id: true }
    });

    const results: MaterialUsagePatternResult[] = [];

    for (const material of materials) {
      try {
        const pattern = await this.analyzeMaterialPattern(material.id);
        if (pattern) {
          results.push(pattern);
        }
      } catch (error) {
        console.error(`Error analyzing material ${material.id}:`, error);
      }
    }

    return results;
  }
}
