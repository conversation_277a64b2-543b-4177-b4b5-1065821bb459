import { PrismaClient } from '@/generated/prisma';
import { CustomerPatternAnalyzer, CustomerPatternResult } from './customer-patterns';
import { MaterialPatternAnalyzer, MaterialUsagePatternResult } from './material-patterns';
import { RevenuePatternAnalyzer, RevenueTrendResult } from './revenue-patterns';

const prisma = new PrismaClient();

export interface PatternCalculationResult {
  calculation_type: string;
  started_at: Date;
  completed_at: Date | null;
  status: 'running' | 'completed' | 'failed';
  records_processed: number;
  records_updated: number;
  error_message?: string;
  execution_time_ms: number;
}

export class PatternCalculationService {
  private customerAnalyzer: CustomerPatternAnalyzer;
  private materialAnalyzer: MaterialPatternAnalyzer;
  private revenueAnalyzer: RevenuePatternAnalyzer;

  constructor() {
    this.customerAnalyzer = new CustomerPatternAnalyzer();
    this.materialAnalyzer = new MaterialPatternAnalyzer();
    this.revenueAnalyzer = new RevenuePatternAnalyzer();
  }

  async calculateAllPatterns(): Promise<PatternCalculationResult[]> {
    const results: PatternCalculationResult[] = [];

    // Calculate customer patterns
    results.push(await this.calculateCustomerPatterns());

    // Calculate material patterns
    results.push(await this.calculateMaterialPatterns());

    // Calculate revenue trends
    results.push(await this.calculateRevenueTrends());

    return results;
  }

  async calculateCustomerPatterns(): Promise<PatternCalculationResult> {
    const startTime = Date.now();
    const calculationType = 'customer_patterns';

    // Log calculation start
    const logEntry = await prisma.patternCalculationLog.create({
      data: {
        calculation_type: calculationType,
        status: 'running'
      }
    });

    try {
      console.log('🔍 Starting customer pattern analysis...');

      // Get all customer patterns
      const customerPatterns = await this.customerAnalyzer.analyzeAllCustomers();

      let recordsUpdated = 0;

      // Save or update patterns in database
      for (const pattern of customerPatterns) {
        await prisma.customerPattern.upsert({
          where: { customer_id: pattern.customer_id },
          update: {
            calculated_at: new Date(),
            calculated_frequency: pattern.calculated_frequency,
            frequency_trend: pattern.frequency_trend,
            last_transaction_days_ago: pattern.last_transaction_days_ago,
            preferred_service_type: pattern.preferred_service_type,
            preferred_service_confidence: pattern.preferred_service_confidence,
            average_weight_kg: pattern.average_weight_kg,
            average_spending: pattern.average_spending,
            seasonal_activity_json: pattern.seasonal_activity_json,
            peak_months: pattern.peak_months,
            low_months: pattern.low_months,
            seasonal_variance: pattern.seasonal_variance,
            loyalty_score: pattern.loyalty_score,
            value_score: pattern.value_score,
            predictability_score: pattern.predictability_score,
            confidence_score: pattern.confidence_score,
            data_points_count: pattern.data_points_count,
            analysis_period_days: pattern.analysis_period_days
          },
          create: {
            customer_id: pattern.customer_id,
            calculated_frequency: pattern.calculated_frequency,
            frequency_trend: pattern.frequency_trend,
            last_transaction_days_ago: pattern.last_transaction_days_ago,
            preferred_service_type: pattern.preferred_service_type,
            preferred_service_confidence: pattern.preferred_service_confidence,
            average_weight_kg: pattern.average_weight_kg,
            average_spending: pattern.average_spending,
            seasonal_activity_json: pattern.seasonal_activity_json,
            peak_months: pattern.peak_months,
            low_months: pattern.low_months,
            seasonal_variance: pattern.seasonal_variance,
            loyalty_score: pattern.loyalty_score,
            value_score: pattern.value_score,
            predictability_score: pattern.predictability_score,
            confidence_score: pattern.confidence_score,
            data_points_count: pattern.data_points_count,
            analysis_period_days: pattern.analysis_period_days
          }
        });
        recordsUpdated++;
      }

      const executionTime = Date.now() - startTime;

      // Update log entry
      await prisma.patternCalculationLog.update({
        where: { id: logEntry.id },
        data: {
          completed_at: new Date(),
          status: 'completed',
          records_processed: customerPatterns.length,
          records_updated: recordsUpdated,
          execution_time_ms: executionTime
        }
      });

      console.log(`✅ Customer pattern analysis completed: ${recordsUpdated} patterns updated in ${executionTime}ms`);

      return {
        calculation_type: calculationType,
        started_at: logEntry.started_at,
        completed_at: new Date(),
        status: 'completed',
        records_processed: customerPatterns.length,
        records_updated: recordsUpdated,
        execution_time_ms: executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Update log entry with error
      await prisma.patternCalculationLog.update({
        where: { id: logEntry.id },
        data: {
          completed_at: new Date(),
          status: 'failed',
          error_message: errorMessage,
          execution_time_ms: executionTime
        }
      });

      console.error('❌ Customer pattern analysis failed:', errorMessage);

      return {
        calculation_type: calculationType,
        started_at: logEntry.started_at,
        completed_at: new Date(),
        status: 'failed',
        records_processed: 0,
        records_updated: 0,
        error_message: errorMessage,
        execution_time_ms: executionTime
      };
    }
  }

  async calculateMaterialPatterns(): Promise<PatternCalculationResult> {
    const startTime = Date.now();
    const calculationType = 'material_patterns';

    const logEntry = await prisma.patternCalculationLog.create({
      data: {
        calculation_type: calculationType,
        status: 'running'
      }
    });

    try {
      console.log('🧴 Starting material pattern analysis...');

      const materialPatterns = await this.materialAnalyzer.analyzeAllMaterials();
      let recordsUpdated = 0;

      for (const pattern of materialPatterns) {
        await prisma.materialUsagePattern.upsert({
          where: { material_id: pattern.material_id },
          update: {
            calculated_at: new Date(),
            average_consumption_rate: pattern.average_consumption_rate,
            consumption_trend: pattern.consumption_trend,
            peak_usage_day_type: pattern.peak_usage_day_type,
            peak_usage_time: pattern.peak_usage_time,
            seasonal_adjustment_json: pattern.seasonal_adjustment_json,
            peak_consumption_months: pattern.peak_consumption_months,
            low_consumption_months: pattern.low_consumption_months,
            seasonal_multiplier: pattern.seasonal_multiplier,
            cost_efficiency_score: pattern.cost_efficiency_score,
            usage_efficiency_score: pattern.usage_efficiency_score,
            waste_indicator: pattern.waste_indicator,
            predicted_days_to_empty: pattern.predicted_days_to_empty,
            reorder_recommendation: pattern.reorder_recommendation,
            optimal_stock_level: pattern.optimal_stock_level,
            confidence_score: pattern.confidence_score,
            data_points_count: pattern.data_points_count,
            analysis_period_days: pattern.analysis_period_days
          },
          create: {
            material_id: pattern.material_id,
            average_consumption_rate: pattern.average_consumption_rate,
            consumption_trend: pattern.consumption_trend,
            peak_usage_day_type: pattern.peak_usage_day_type,
            peak_usage_time: pattern.peak_usage_time,
            seasonal_adjustment_json: pattern.seasonal_adjustment_json,
            peak_consumption_months: pattern.peak_consumption_months,
            low_consumption_months: pattern.low_consumption_months,
            seasonal_multiplier: pattern.seasonal_multiplier,
            cost_efficiency_score: pattern.cost_efficiency_score,
            usage_efficiency_score: pattern.usage_efficiency_score,
            waste_indicator: pattern.waste_indicator,
            predicted_days_to_empty: pattern.predicted_days_to_empty,
            reorder_recommendation: pattern.reorder_recommendation,
            optimal_stock_level: pattern.optimal_stock_level,
            confidence_score: pattern.confidence_score,
            data_points_count: pattern.data_points_count,
            analysis_period_days: pattern.analysis_period_days
          }
        });
        recordsUpdated++;
      }

      const executionTime = Date.now() - startTime;

      await prisma.patternCalculationLog.update({
        where: { id: logEntry.id },
        data: {
          completed_at: new Date(),
          status: 'completed',
          records_processed: materialPatterns.length,
          records_updated: recordsUpdated,
          execution_time_ms: executionTime
        }
      });

      console.log(`✅ Material pattern analysis completed: ${recordsUpdated} patterns updated in ${executionTime}ms`);

      return {
        calculation_type: calculationType,
        started_at: logEntry.started_at,
        completed_at: new Date(),
        status: 'completed',
        records_processed: materialPatterns.length,
        records_updated: recordsUpdated,
        execution_time_ms: executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      await prisma.patternCalculationLog.update({
        where: { id: logEntry.id },
        data: {
          completed_at: new Date(),
          status: 'failed',
          error_message: errorMessage,
          execution_time_ms: executionTime
        }
      });

      console.error('❌ Material pattern analysis failed:', errorMessage);

      return {
        calculation_type: calculationType,
        started_at: logEntry.started_at,
        completed_at: new Date(),
        status: 'failed',
        records_processed: 0,
        records_updated: 0,
        error_message: errorMessage,
        execution_time_ms: executionTime
      };
    }
  }

  async calculateRevenueTrends(): Promise<PatternCalculationResult> {
    const startTime = Date.now();
    const calculationType = 'revenue_trends';

    const logEntry = await prisma.patternCalculationLog.create({
      data: {
        calculation_type: calculationType,
        status: 'running'
      }
    });

    try {
      console.log('💰 Starting revenue trend analysis...');

      // Analyze last 90 days
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - 90 * 24 * 60 * 60 * 1000);

      const revenueTrends = await this.revenueAnalyzer.analyzeRevenueForDateRange(startDate, endDate);
      let recordsUpdated = 0;

      for (const trend of revenueTrends) {
        await prisma.revenueTrend.upsert({
          where: { date: trend.date },
          update: {
            daily_revenue: trend.daily_revenue,
            daily_transaction_count: trend.daily_transaction_count,
            daily_avg_transaction: trend.daily_avg_transaction,
            daily_weight_total: trend.daily_weight_total,
            weekly_avg_revenue: trend.weekly_avg_revenue,
            weekly_transaction_count: trend.weekly_transaction_count,
            week_day_rank: trend.week_day_rank,
            monthly_avg_revenue: trend.monthly_avg_revenue,
            monthly_transaction_count: trend.monthly_transaction_count,
            month_day_rank: trend.month_day_rank,
            revenue_trend: trend.revenue_trend,
            peak_status: trend.peak_status,
            growth_rate: trend.growth_rate,
            day_type: trend.day_type,
            weather_context: trend.weather_context,
            seasonal_factor: trend.seasonal_factor,
            confidence_score: trend.confidence_score,
            data_completeness: trend.data_completeness
          },
          create: {
            date: trend.date,
            daily_revenue: trend.daily_revenue,
            daily_transaction_count: trend.daily_transaction_count,
            daily_avg_transaction: trend.daily_avg_transaction,
            daily_weight_total: trend.daily_weight_total,
            weekly_avg_revenue: trend.weekly_avg_revenue,
            weekly_transaction_count: trend.weekly_transaction_count,
            week_day_rank: trend.week_day_rank,
            monthly_avg_revenue: trend.monthly_avg_revenue,
            monthly_transaction_count: trend.monthly_transaction_count,
            month_day_rank: trend.month_day_rank,
            revenue_trend: trend.revenue_trend,
            peak_status: trend.peak_status,
            growth_rate: trend.growth_rate,
            day_type: trend.day_type,
            weather_context: trend.weather_context,
            seasonal_factor: trend.seasonal_factor,
            confidence_score: trend.confidence_score,
            data_completeness: trend.data_completeness
          }
        });
        recordsUpdated++;
      }

      const executionTime = Date.now() - startTime;

      await prisma.patternCalculationLog.update({
        where: { id: logEntry.id },
        data: {
          completed_at: new Date(),
          status: 'completed',
          records_processed: revenueTrends.length,
          records_updated: recordsUpdated,
          execution_time_ms: executionTime
        }
      });

      console.log(`✅ Revenue trend analysis completed: ${recordsUpdated} trends updated in ${executionTime}ms`);

      return {
        calculation_type: calculationType,
        started_at: logEntry.started_at,
        completed_at: new Date(),
        status: 'completed',
        records_processed: revenueTrends.length,
        records_updated: recordsUpdated,
        execution_time_ms: executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      await prisma.patternCalculationLog.update({
        where: { id: logEntry.id },
        data: {
          completed_at: new Date(),
          status: 'failed',
          error_message: errorMessage,
          execution_time_ms: executionTime
        }
      });

      console.error('❌ Revenue trend analysis failed:', errorMessage);

      return {
        calculation_type: calculationType,
        started_at: logEntry.started_at,
        completed_at: new Date(),
        status: 'failed',
        records_processed: 0,
        records_updated: 0,
        error_message: errorMessage,
        execution_time_ms: executionTime
      };
    }
  }

  async getCalculationHistory(limit: number = 10): Promise<any[]> {
    return prisma.patternCalculationLog.findMany({
      orderBy: { started_at: 'desc' },
      take: limit
    });
  }
}
