import { PrismaClient, DayType, WeatherContext } from '@/generated/prisma';

const prisma = new PrismaClient();

export interface RevenueTrendResult {
  date: Date;
  daily_revenue: number;
  daily_transaction_count: number;
  daily_avg_transaction: number;
  daily_weight_total: number;
  weekly_avg_revenue: number;
  weekly_transaction_count: number;
  week_day_rank: number;
  monthly_avg_revenue: number;
  monthly_transaction_count: number;
  month_day_rank: number;
  revenue_trend: 'peak' | 'high' | 'normal' | 'low' | 'valley';
  peak_status: 'daily_peak' | 'weekly_peak' | 'monthly_peak' | 'normal';
  growth_rate: number;
  day_type: DayType;
  weather_context: WeatherContext;
  seasonal_factor: number;
  confidence_score: number;
  data_completeness: number;
}

export class RevenuePatternAnalyzer {
  private readonly ANALYSIS_PERIOD_DAYS = 365; // Analyze last year

  async analyzeRevenueTrends(startDate?: Date, endDate?: Date): Promise<RevenueTrendResult[]> {
    const analysisEndDate = endDate || new Date();
    const analysisStartDate = startDate || new Date(analysisEndDate.getTime() - this.ANALYSIS_PERIOD_DAYS * 24 * 60 * 60 * 1000);

    // Get all transactions in the period
    const transactions = await prisma.transaction.findMany({
      where: {
        transaction_date: {
          gte: analysisStartDate,
          lte: analysisEndDate
        }
      },
      orderBy: { transaction_date: 'asc' }
    });

    // Group transactions by date
    const dailyData = this.groupTransactionsByDate(transactions);
    
    // Calculate trends for each day
    const results: RevenueTrendResult[] = [];
    const dates = this.generateDateRange(analysisStartDate, analysisEndDate);

    for (const date of dates) {
      const dateKey = this.formatDateKey(date);
      const dayData = dailyData[dateKey] || [];
      
      const trendResult = await this.analyzeDayTrend(date, dayData, dailyData);
      results.push(trendResult);
    }

    return results;
  }

  private groupTransactionsByDate(transactions: any[]): Record<string, any[]> {
    return transactions.reduce((acc, transaction) => {
      const dateKey = this.formatDateKey(new Date(transaction.transaction_date));
      if (!acc[dateKey]) {
        acc[dateKey] = [];
      }
      acc[dateKey].push(transaction);
      return acc;
    }, {} as Record<string, any[]>);
  }

  private formatDateKey(date: Date): string {
    return date.toISOString().split('T')[0]; // YYYY-MM-DD
  }

  private generateDateRange(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return dates;
  }

  private async analyzeDayTrend(date: Date, dayTransactions: any[], allDailyData: Record<string, any[]>): Promise<RevenueTrendResult> {
    // Calculate daily metrics
    const daily_revenue = dayTransactions.reduce((sum, t) => sum + t.price, 0);
    const daily_transaction_count = dayTransactions.length;
    const daily_avg_transaction = daily_transaction_count > 0 ? daily_revenue / daily_transaction_count : 0;
    const daily_weight_total = dayTransactions.reduce((sum, t) => sum + t.weight_kg, 0);

    // Calculate weekly context
    const weeklyMetrics = this.calculateWeeklyMetrics(date, allDailyData);
    
    // Calculate monthly context
    const monthlyMetrics = this.calculateMonthlyMetrics(date, allDailyData);
    
    // Determine trend classification
    const trendClassification = this.classifyRevenueTrend(
      daily_revenue, 
      weeklyMetrics.weekly_avg_revenue, 
      monthlyMetrics.monthly_avg_revenue
    );

    // Determine peak status
    const peakStatus = this.determinePeakStatus(
      daily_revenue,
      weeklyMetrics,
      monthlyMetrics,
      allDailyData
    );

    // Calculate growth rate
    const growth_rate = this.calculateGrowthRate(date, daily_revenue, allDailyData);

    // Get contextual data
    const contextualData = this.getContextualData(date, dayTransactions);

    // Calculate confidence metrics
    const confidenceMetrics = this.calculateConfidenceMetrics(dayTransactions, date);

    return {
      date,
      daily_revenue,
      daily_transaction_count,
      daily_avg_transaction,
      daily_weight_total,
      ...weeklyMetrics,
      ...monthlyMetrics,
      ...trendClassification,
      peak_status: peakStatus,
      growth_rate,
      ...contextualData,
      ...confidenceMetrics
    };
  }

  private calculateWeeklyMetrics(date: Date, allDailyData: Record<string, any[]>) {
    // Get week start (Monday)
    const weekStart = new Date(date);
    weekStart.setDate(date.getDate() - date.getDay() + 1);
    
    const weekDays: Date[] = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(weekStart);
      day.setDate(weekStart.getDate() + i);
      weekDays.push(day);
    }

    const weeklyRevenues = weekDays.map(day => {
      const dayKey = this.formatDateKey(day);
      const dayData = allDailyData[dayKey] || [];
      return dayData.reduce((sum, t) => sum + t.price, 0);
    });

    const weeklyTransactionCounts = weekDays.map(day => {
      const dayKey = this.formatDateKey(day);
      const dayData = allDailyData[dayKey] || [];
      return dayData.length;
    });

    const weekly_avg_revenue = weeklyRevenues.reduce((sum, rev) => sum + rev, 0) / 7;
    const weekly_transaction_count = weeklyTransactionCounts.reduce((sum, count) => sum + count, 0);

    // Rank this day within the week (1 = highest revenue)
    const currentDayRevenue = weeklyRevenues[date.getDay() === 0 ? 6 : date.getDay() - 1]; // Adjust for Monday start
    const sortedWeeklyRevenues = [...weeklyRevenues].sort((a, b) => b - a);
    const week_day_rank = sortedWeeklyRevenues.indexOf(currentDayRevenue) + 1;

    return {
      weekly_avg_revenue,
      weekly_transaction_count,
      week_day_rank
    };
  }

  private calculateMonthlyMetrics(date: Date, allDailyData: Record<string, any[]>) {
    const year = date.getFullYear();
    const month = date.getMonth();
    
    // Get all days in the month
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const monthlyRevenues: number[] = [];
    
    for (let day = 1; day <= daysInMonth; day++) {
      const dayDate = new Date(year, month, day);
      const dayKey = this.formatDateKey(dayDate);
      const dayData = allDailyData[dayKey] || [];
      const dayRevenue = dayData.reduce((sum, t) => sum + t.price, 0);
      monthlyRevenues.push(dayRevenue);
    }

    const monthly_avg_revenue = monthlyRevenues.reduce((sum, rev) => sum + rev, 0) / daysInMonth;
    const monthly_transaction_count = monthlyRevenues.length; // This should be calculated properly

    // Rank this day within the month
    const currentDayRevenue = monthlyRevenues[date.getDate() - 1];
    const sortedMonthlyRevenues = [...monthlyRevenues].sort((a, b) => b - a);
    const month_day_rank = sortedMonthlyRevenues.indexOf(currentDayRevenue) + 1;

    return {
      monthly_avg_revenue,
      monthly_transaction_count,
      month_day_rank
    };
  }

  private classifyRevenueTrend(dailyRevenue: number, weeklyAvg: number, monthlyAvg: number) {
    const weeklyRatio = weeklyAvg > 0 ? dailyRevenue / weeklyAvg : 0;
    const monthlyRatio = monthlyAvg > 0 ? dailyRevenue / monthlyAvg : 0;
    
    const avgRatio = (weeklyRatio + monthlyRatio) / 2;

    let revenue_trend: 'peak' | 'high' | 'normal' | 'low' | 'valley';
    
    if (avgRatio >= 1.5) {
      revenue_trend = 'peak';
    } else if (avgRatio >= 1.2) {
      revenue_trend = 'high';
    } else if (avgRatio >= 0.8) {
      revenue_trend = 'normal';
    } else if (avgRatio >= 0.5) {
      revenue_trend = 'low';
    } else {
      revenue_trend = 'valley';
    }

    return { revenue_trend };
  }

  private determinePeakStatus(
    dailyRevenue: number, 
    weeklyMetrics: any, 
    monthlyMetrics: any, 
    allDailyData: Record<string, any[]>
  ): 'daily_peak' | 'weekly_peak' | 'monthly_peak' | 'normal' {
    
    if (weeklyMetrics.week_day_rank === 1 && monthlyMetrics.month_day_rank === 1) {
      return 'monthly_peak';
    } else if (weeklyMetrics.week_day_rank === 1) {
      return 'weekly_peak';
    } else if (dailyRevenue > weeklyMetrics.weekly_avg_revenue * 1.3) {
      return 'daily_peak';
    } else {
      return 'normal';
    }
  }

  private calculateGrowthRate(date: Date, dailyRevenue: number, allDailyData: Record<string, any[]>): number {
    // Compare with same day previous week
    const previousWeekDate = new Date(date);
    previousWeekDate.setDate(date.getDate() - 7);
    
    const previousWeekKey = this.formatDateKey(previousWeekDate);
    const previousWeekData = allDailyData[previousWeekKey] || [];
    const previousWeekRevenue = previousWeekData.reduce((sum, t) => sum + t.price, 0);

    if (previousWeekRevenue === 0) return 0;
    
    return (dailyRevenue - previousWeekRevenue) / previousWeekRevenue;
  }

  private getContextualData(date: Date, dayTransactions: any[]) {
    // Determine day type
    const dayOfWeek = date.getDay();
    const day_type = (dayOfWeek === 0 || dayOfWeek === 6) ? DayType.WEEKEND : DayType.WEEKDAY;

    // Get most common weather context for the day
    const weatherCounts = dayTransactions.reduce((acc, t) => {
      acc[t.context_weather] = (acc[t.context_weather] || 0) + 1;
      return acc;
    }, {} as Record<WeatherContext, number>);

    const weather_context = Object.entries(weatherCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] as WeatherContext || WeatherContext.SUNNY;

    // Calculate average seasonal factor
    const seasonal_factor = dayTransactions.length > 0 
      ? dayTransactions.reduce((sum, t) => sum + t.context_seasonal_factor, 0) / dayTransactions.length
      : 0.5;

    return {
      day_type,
      weather_context,
      seasonal_factor
    };
  }

  private calculateConfidenceMetrics(dayTransactions: any[], date: Date) {
    // Data completeness: based on transaction count vs expected
    const expectedTransactionsPerDay = 5; // Baseline expectation
    const data_completeness = Math.min(dayTransactions.length / expectedTransactionsPerDay, 1);

    // Confidence score: based on data completeness and recency
    const daysAgo = (Date.now() - date.getTime()) / (24 * 60 * 60 * 1000);
    const recencyFactor = Math.max(0, 1 - daysAgo / 365); // More recent = higher confidence
    
    const confidence_score = (data_completeness * 0.7 + recencyFactor * 0.3);

    return {
      confidence_score,
      data_completeness
    };
  }

  async analyzeRevenueForDateRange(startDate: Date, endDate: Date): Promise<RevenueTrendResult[]> {
    return this.analyzeRevenueTrends(startDate, endDate);
  }
}
